{"objectType": "Activity", "id": "http://www.example.com/meetings/occurances/34534", "definition": {"type": "http://adlnet.gov/expapi/activities/meeting", "name": {"en-GB": "example meeting", "en-US": "example meeting"}, "description": {"en-GB": "An example meeting that happened on a specific occasion with certain people present.", "en-US": "An example meeting that happened on a specific occasion with certain people present."}, "moreInfo": "http://virtualmeeting.example.com/345256", "extensions": {"http://example.com/profiles/meetings/extension/location": "X:\\meetings\\minutes\\examplemeeting.one", "http://example.com/profiles/meetings/extension/reporter": {"name": "<PERSON>", "id": "http://openid.com/342"}}}}
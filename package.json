{"name": "adl-lrs-conformance-tests", "version": "2.0.0-0", "description": "lrs-conformance-tests", "main": "./bin/console_runner.js", "scripts": {"test": "node node_modules/mocha/bin/mocha test/v1_0_2/*.js --reporter nyan", "start": "node ./bin/console_runner.js", "update-batteries": "node ./bin/update-batteries.js"}, "repository": {"type": "git", "url": "git://github.com/adlnet/lrs-conformance-test-suite.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/adlnet/lrs-conformance-test-suite/issues"}, "bin": {"lrs-test": "./bin/console_runner.js"}, "homepage": "https://github.com/adlnet/lrs-conformance-test-suite", "dependencies": {"axios": "^1.4.0", "axios-oauth-1.0a": "^0.3.6", "chai": "^2.3.0", "chai-things": "^0.2.0", "colors": "^1.1.2", "comb": "^1.0.1", "commander": "^2.6.0", "cookie-parser": "^1.4.1", "dirty-chai": "^1.0.0", "exit": "^0.1.2", "express": "^4.14.1", "extend": "^2.0.0", "form-data": "^1.0.0-rc4", "form-urlencoded": "0.0.7", "isemail": "^1.1.1", "joi": "^6.4.1", "jsonschema": "^1.1.0", "jws": "^3.1.3", "lodash.isequal": "^4.5.0", "mocha": "^1.20.1", "moment": "^2.8.4", "node-env-file": "0.1.3", "oauth": "^0.9.14", "open": "0.0.5", "pretty-error": "^2.0.0", "q": "^1.1.2", "request": "^2.37.0", "should": "^4.0.4", "string": "^3.1.0", "super-request": "file:./local/super-request", "superagent-oauth": "^0.2.3", "supertest": "file:./local/supertest", "supertest-as-promised": "^1.0.0", "uuid": "^9.0.0", "validator": "^5.7.0"}}
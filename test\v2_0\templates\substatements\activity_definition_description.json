{"objectType": "SubStatement", "actor": {"objectType": "Agent", "name": "xAPI mbox_sha1sum", "mbox_sha1sum": "cd9b00a5611f94eaa7b1661edab976068e364975"}, "verb": {"id": "http://adlnet.gov/expapi/verbs/reported", "display": {"en-GB": "reported", "en-US": "reported"}}, "object": {"objectType": "Activity", "id": "http://www.example.com/meetings/occurances/34534", "definition": {"type": "http://adlnet.gov/expapi/activities/meeting", "name": {"en-GB": "example meeting", "en-US": "example meeting"}, "description": {"en-GB": "An example meeting that happened on a specific occasion with certain people present.", "en-US": "An example meeting that happened on a specific occasion with certain people present.", "ase": "An example meeting that happened on a specific occasion with certain people present."}, "moreInfo": "http://virtualmeeting.example.com/345256", "extensions": {"http://example.com/profiles/meetings/extension/location": "X:\\meetings\\minutes\\examplemeeting.one", "http://example.com/profiles/meetings/extension/reporter": {"name": "<PERSON>", "id": "http://openid.com/342"}}}}}
/**
 * Description : This is a test suite that tests an LRS endpoint based on the testing requirements document
 * found at https://github.com/adlnet/xapi-lrs-conformance-requirements
 */

(function(module, fs, extend, moment, request, requestPromise, chai, liburl, <PERSON><PERSON>, helper, multipartParser, redirect)
{
<<<<<<< HEAD
	if (global.OAUTH)
	{
		/**  XAPI-00334, Communication 2.1.3 GET Statements
		 * An LRS rejects a Statement of bad authorization (either authentication needed or failed credentials) with error code 401 Unauthorized 
		 */
		describe('An LRS rejects a Statement of bad authorization (either authentication needed or failed credentials) with error code 401 Unauthorized (Authentication 4.0 XAPI-00334)', function()
		{
=======

describe('Authentication Requirements (Communication 4.0)', function() {


/**  XAPI-00334, Communication 2.1.3 GET Statements
 * An LRS rejects a Statement of bad authorization (either authentication needed or failed credentials) with error code 401 Unauthorized
 */
 describe('An LRS rejects a Statement of bad authorization (either authentication needed or failed credentials) with error code 401 Unauthorized (Authentication 4.0 XAPI-00334)',function(){
>>>>>>> 4bd84a249509fd6f332220f396c50d0efe1b6c07


			it("fails when given a random name pass pair", function(done)
			{
				var templates = [
				{
					statement: '{{statements.default}}'
				}];
				var data = helper.createFromTemplate(templates);
				data = data.statement;
				data.id = helper.generateUUID();
				var headers = helper.addAllHeaders(
				{});

<<<<<<< HEAD
				//warning: this ".\\" is super important. Node caches the modules, and the superrequest module has been modified to work correctly
				//with oauth already. We get a new verions by appending some other characters to defeat the cache. 
				if (global.OAUTH)
					request = require('.\\super-request');
				else
					headers["Authorization"] = 'Basic ' + new Buffer('RobCIsNot:AUserOnThisLRS').toString('base64');
				request(helper.getEndpointAndAuth())
					.put(helper.getEndpointStatements() + '?statementId=' + data.id)
					.headers(headers)
					.json(data)
					.expect(401, done);
			});
=======
        //warning: this ".\\" is super important. Node caches the modules, and the superrequest module has been modified to work correctly
        //with oauth already. We get a new verions by appending some other characters to defeat the cache.
        if(global.OAUTH)
            request = require('.\\super-request');
        else
            headers["Authorization"] = 'Basic ' + new Buffer('RobCIsNot:AUserOnThisLRS').toString('base64');
            request(helper.getEndpointAndAuth())
            .put(helper.getEndpointStatements() + '?statementId=' + data.id)
            .headers(headers)
            .json(data)
            .expect(401, done);
    });
>>>>>>> 4bd84a249509fd6f332220f396c50d0efe1b6c07

			it('fails with a malformed header', function(done)
			{
				var templates = [
				{
					statement: '{{statements.default}}'
				}];
				var data = helper.createFromTemplate(templates);
				data = data.statement;
				data.id = helper.generateUUID();
				var headers = helper.addAllHeaders(
				{});

<<<<<<< HEAD
				//warning: this ".\\" is super important. Node caches the modules, and the superrequest module has been modified to work correctly
				//with oauth already. We get a new verions by appending some other characters to defeat the cache. 
				if (global.OAUTH)
					request = require('.\\super-request');
				else
					headers["Authorization"] = 'Basic:' + new Buffer('RobCIsNot:AUserOnThisLRS').toString('base64'); //note bad encoding here.
				request(helper.getEndpointAndAuth())
					.put(helper.getEndpointStatements() + '?statementId=' + data.id)
					.headers(headers)
					.json(data)
					.expect(401, done);
			});
		})


		/**  XAPI-00335, Communication 2.1.3 GET Statements
		 * An LRS must support HTTP Basic Authentication
		 */
		//WARNING: This might not be a great test. OAUTH will override it
		it('An LRS must support HTTP Basic Authentication(Authentication 4.0 XAPI-00335)', function(done)
		{
			var templates = [
			{
				statement: '{{statements.default}}'
			}];
			var data = helper.createFromTemplate(templates);
			data = data.statement;
			data.id = helper.generateUUID();
			var headers = helper.addAllHeaders(
			{});

			request(helper.getEndpointAndAuth())
				.put(helper.getEndpointStatements() + '?statementId=' + data.id)
				.headers(headers)
				.json(data)
				.expect(204, done);
		});
	}
}(module, require('fs'), require('extend'), require('moment'), require('super-request'), require('supertest-as-promised'), require('chai'), require('url'), require('joi'), require('./../helper'), require('./../multipartParser'), require('./../redirect.js')));
=======
        //warning: this ".\\" is super important. Node caches the modules, and the superrequest module has been modified to work correctly
        //with oauth already. We get a new verions by appending some other characters to defeat the cache.
        if(global.OAUTH)
            request = require('.\\super-request');
        else
            headers["Authorization"] = 'Basic:' + new Buffer('RobCIsNot:AUserOnThisLRS').toString('base64'); //note bad encoding here.
            request(helper.getEndpointAndAuth())
            .put(helper.getEndpointStatements() + '?statementId=' + data.id)
            .headers(headers)
            .json(data)
            .expect(401, done);
    });
});

/**  XAPI-00335, Communication 2.1.3 GET Statements
 * An LRS must support HTTP Basic Authentication
 */
 //WARNING: This might not be a great test. OAUTH will override it
    it('An LRS must support HTTP Basic Authentication(Authentication 4.0 XAPI-00335)', function(done)
    {
        var templates = [
        {
            statement: '{{statements.default}}'
        }];
        var data = helper.createFromTemplate(templates);
        data = data.statement;
        data.id = helper.generateUUID();
        var headers = helper.addAllHeaders(
        {});

        request(helper.getEndpointAndAuth())
            .put(helper.getEndpointStatements() + '?statementId=' + data.id)
            .headers(headers)
            .json(data)
            .expect(204, done);
    });

});

}(module, require('fs'), require('extend'), require('moment'), require('super-request'), require('supertest-as-promised'), require('chai'), require('url'), require('joi'), require('./../helper'), require('./../multipartParser'), require('./../redirect.js')));
>>>>>>> 4bd84a249509fd6f332220f396c50d0efe1b6c07

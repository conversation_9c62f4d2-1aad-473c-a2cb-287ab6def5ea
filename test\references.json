{"Activities Verify Templates": {"name": "Activities Verify Templates"}, "should pass statement activity default template": {"name": "should pass statement activity default template"}, "should pass statement substatement activity default template": {"name": "should pass statement substatement activity default template"}, "should pass statement activity choice template": {"name": "should pass statement activity choice template"}, "should pass statement activity likert template": {"name": "should pass statement activity likert template"}, "should pass statement activity matching template": {"name": "should pass statement activity matching template"}, "should pass statement activity performance template": {"name": "should pass statement activity performance template"}, "should pass statement activity sequencing template": {"name": "should pass statement activity sequencing template"}, "should pass statement substatement activity choice template": {"name": "should pass statement substatement activity choice template"}, "should pass statement substatement activity likert template": {"name": "should pass statement substatement activity likert template"}, "should pass statement substatement activity matching template": {"name": "should pass statement substatement activity matching template"}, "should pass statement substatement activity performance template": {"name": "should pass statement substatement activity performance template"}, "should pass statement substatement activity sequencing template": {"name": "should pass statement substatement activity sequencing template"}, "An \"object\" property uses the \"id\" property exactly one time": {"name": "An \"object\" property uses the \"id\" property exactly one time", "1.0.2_ref_text": "Multiplicity, *******.table1.row2.b", "1.0.2_ref": "*******.table1.row2.b", "1.0.3_ref": "Data *******.s1.table1.row2", "1.0.3_link": ["Data.md#*******.s1.table1.row2"]}, "statement activity \"id\" not provided": {"name": "statement activity \"id\" not provided"}, "statement substatement activity \"id\" not provided": {"name": "statement substatement activity \"id\" not provided"}, "An \"object\" property's \"id\" property is an IRI": {"name": "An \"object\" property's \"id\" property is an IRI", "1.0.2_ref_text": "Type, *******.table1.row2.a", "1.0.2_ref": "*******.table1.row2.a", "1.0.3_ref": "Data *******.s1.table1.row2", "1.0.3_link": ["Data.md#*******.s1.table1.row2"]}, "statement activity \"id\" not IRI": {"name": "statement activity \"id\" not IRI"}, "statement activity \"id\" is IRI": {"name": "statement activity \"id\" is IRI"}, "statement substatement activity \"id\" not IRI": {"name": "statement substatement activity \"id\" not IRI"}, "statement substatement activity \"id\" is IRI": {"name": "statement substatement activity \"id\" is IRI"}, "An Activity Definition is defined as the contents of a \"definition\" property object of an Activity": {"name": "An Activity Definition is defined as the contents of a \"definition\" property object of an Activity", "1.0.2_ref_text": "Format, *******.table2", "1.0.2_ref": "*******.table2", "1.0.3_ref": "Data *******.s1.table1.row3", "1.0.3_link": ["Data.md#*******.s1.table1.row3"]}, "statement activity \"definition\" not object": {"name": "statement activity \"definition\" not object"}, "statement substatement activity \"definition\" not object": {"name": "statement substatement activity \"definition\" not object"}, "An Activity's \"definition\" property is an Object": {"name": "An Activity's \"definition\" property is an Object", "1.0.2_ref_text": "Type, *******.table1.row3.a", "1.0.2_ref": "*******.table1.row3.a", "1.0.3_ref": "Data *******.s1.table1.row3", "1.0.3_link": ["Data.md#*******.s1.table1.row3"]}, "An Activity Definition uses the following properties: name, description, type, moreInfo, interactionType, or extensions": {"name": "An Activity Definition uses the following properties: name, description, type, moreInfo, interactionType, or extensions", "1.0.2_ref_text": "Format, *******.table2, *******.table3", "1.0.2_ref": "*******.table2, *******.table3", "1.0.3_ref": "Data *******.s2", "1.0.3_link": ["Data.md#*******.s2"]}, "statement activity \"definition\" missing all properties": {"name": "statement activity \"definition\" missing all properties"}, "statement activity \"definition\" contains \"name\"": {"name": "statement activity \"definition\" contains \"name\""}, "statement activity \"definition\" contains \"description\"": {"name": "statement activity \"definition\" contains \"description\""}, "statement activity \"definition\" contains \"type\"": {"name": "statement activity \"definition\" contains \"type\""}, "statement activity \"definition\" contains \"moreInfo\"": {"name": "statement activity \"definition\" contains \"moreInfo\""}, "statement activity \"definition\" contains \"extensions\"": {"name": "statement activity \"definition\" contains \"extensions\""}, "statement activity \"definition\" contains \"interactionType\"": {"name": "statement activity \"definition\" contains \"interactionType\""}, "statement substatement activity \"definition\" missing all properties": {"name": "statement substatement activity \"definition\" missing all properties"}, "statement substatement activity \"definition\" contains \"name\"": {"name": "statement substatement activity \"definition\" contains \"name\""}, "statement substatement activity \"definition\" contains \"description\"": {"name": "statement substatement activity \"definition\" contains \"description\""}, "statement substatement activity \"definition\" contains \"type\"": {"name": "statement substatement activity \"definition\" contains \"type\""}, "statement substatement activity \"definition\" contains \"moreInfo\"": {"name": "statement substatement activity \"definition\" contains \"moreInfo\""}, "statement substatement activity \"definition\" contains \"extensions\"": {"name": "statement substatement activity \"definition\" contains \"extensions\""}, "statement substatement activity \"definition\" contains \"interactionType\"": {"name": "statement substatement activity \"definition\" contains \"interactionType\""}, "An Activity Definition's \"name\" property is a Language Map": {"name": "An Activity Definition's \"name\" property is a Language Map", "1.0.2_ref_text": "Type, *******.table2.row1.a", "1.0.2_ref": "*******.table2.row1.a", "1.0.3_ref": "Data *******.s2.table1.row1", "1.0.3_link": ["Data.md#*******.s2.table1.row1"]}, "statement object \"name\" language map is numeric": {"name": "statement object \"name\" language map is numeric"}, "statement object \"name\" language map is string": {"name": "statement object \"name\" language map is string"}, "statement substatement activity \"name\" language map is numeric": {"name": "statement substatement activity \"name\" language map is numeric"}, "statement substatement activity \"name\" language map is string": {"name": "statement substatement activity \"name\" language map is string"}, "An Activity Definition's \"description\" property is a Language Map": {"name": "An Activity Definition's \"description\" property is a Language Map", "1.0.2_ref_text": "Type, *******.table2.row2.a", "1.0.2_ref": "*******.table2.row2.a", "1.0.3_ref": "Data *******.s2.table1.row2", "1.0.3_link": ["Data.md#*******.s2.table1.row2"]}, "statement object \"description\" language map is numeric": {"name": "statement object \"description\" language map is numeric"}, "statement object \"description\" language map is string": {"name": "statement object \"description\" language map is string"}, "statement substatement activity \"description\" language map is numeric": {"name": "statement substatement activity \"description\" language map is numeric"}, "statement substatement activity \"description\" language map is string": {"name": "statement substatement activity \"description\" language map is string"}, "An Activity Definition's \"type\" property is an IRI": {"name": "An Activity Definition's \"type\" property is an IRI", "1.0.2_ref_text": "Type, *******.table2.row3.a", "1.0.2_ref": "*******.table2.row3.a", "1.0.3_ref": "Data *******.s2.table1.row3", "1.0.3_link": ["Data.md#*******.s2.table1.row3"]}, "statement activity \"type\" not IRI": {"name": "statement activity \"type\" not IRI"}, "statement substatement activity \"type\" not IRI": {"name": "statement substatement activity \"type\" not IRI"}, "An Activity Definition's \"moreinfo\" property is an IRL": {"name": "An Activity Definition's \"moreinfo\" property is an IRL", "1.0.2_ref_text": "Type, *******.table2.row4.a", "1.0.2_ref": "*******.table2.row4.a", "1.0.3_ref": "Data *******.s2.table1.row4", "1.0.3_link": ["Data.md#*******.s2.table1.row4"]}, "statement activity \"moreInfo\" not IRI": {"name": "statement activity \"moreInfo\" not IRI"}, "statement substatement activity \"moreInfo\" not IRI": {"name": "statement substatement activity \"moreInfo\" not IRI"}, "An Activity Definition's \"interactionType\" property is a String with a value of either “true-false”, “choice”, “fill-in”, “long-fill-in”, “matching”, “performance”, “sequencing”, “likert”, “numeric” or “other”": {"name": "An Activity Definition's \"interactionType\" property is a String with a value of either “true-false”, “choice”, “fill-in”, “long-fill-in”, “matching”, “performance”, “sequencing”, “likert”, “numeric” or “other”", "1.0.2_ref_text": "*******.table3.row1.a, SCORM 2004 4th Edition RTE Book", "1.0.2_ref": "*******.table3.row1.a, SCORM 2004 4th Edition RTE Book", "1.0.3_ref": "Data *******.s8.table1.row1", "1.0.3_link": ["Data.md#*******.s8.table1.row1"]}, "statement activity \"interactionType\" can be used with \"true-false\"": {"name": "statement activity \"interactionType\" can be used with \"true-false\""}, "statement activity \"interactionType\" can be used with \"choice\"": {"name": "statement activity \"interactionType\" can be used with \"choice\""}, "statement activity \"interactionType\" can be used with \"fill-in\"": {"name": "statement activity \"interactionType\" can be used with \"fill-in\""}, "statement activity \"interactionType\" can be used with \"long-fill-in\"": {"name": "statement activity \"interactionType\" can be used with \"long-fill-in\""}, "statement activity \"interactionType\" can be used with \"matching\"": {"name": "statement activity \"interactionType\" can be used with \"matching\""}, "statement activity \"interactionType\" can be used with \"performance\"": {"name": "statement activity \"interactionType\" can be used with \"performance\""}, "statement activity \"interactionType\" can be used with \"sequencing\"": {"name": "statement activity \"interactionType\" can be used with \"sequencing\""}, "statement activity \"interactionType\" can be used with \"likert\"": {"name": "statement activity \"interactionType\" can be used with \"likert\""}, "statement activity \"interactionType\" can be used with \"numeric\"": {"name": "statement activity \"interactionType\" can be used with \"numeric\""}, "statement activity \"interactionType\" can be used with \"other\"": {"name": "statement activity \"interactionType\" can be used with \"other\""}, "statement substatement activity \"interactionType\" can be used with \"true-false\"": {"name": "statement substatement activity \"interactionType\" can be used with \"true-false\""}, "statement substatement activity \"interactionType\" can be used with \"choice\"": {"name": "statement substatement activity \"interactionType\" can be used with \"choice\""}, "statement substatement activity \"interactionType\" can be used with \"fill-in\"": {"name": "statement substatement activity \"interactionType\" can be used with \"fill-in\""}, "statement substatement activity \"interactionType\" can be used with \"long-fill-in\"": {"name": "statement substatement activity \"interactionType\" can be used with \"long-fill-in\""}, "statement substatement activity \"interactionType\" can be used with \"matching\"": {"name": "statement substatement activity \"interactionType\" can be used with \"matching\""}, "statement substatement activity \"interactionType\" can be used with \"performance\"": {"name": "statement substatement activity \"interactionType\" can be used with \"performance\""}, "statement substatement activity \"interactionType\" can be used with \"sequencing\"": {"name": "statement substatement activity \"interactionType\" can be used with \"sequencing\""}, "statement substatement activity \"interactionType\" can be used with \"likert\"": {"name": "statement substatement activity \"interactionType\" can be used with \"likert\""}, "statement substatement activity \"interactionType\" can be used with \"numeric\"": {"name": "statement substatement activity \"interactionType\" can be used with \"numeric\""}, "statement substatement activity \"interactionType\" can be used with \"other\"": {"name": "statement substatement activity \"interactionType\" can be used with \"other\""}, "An Activity Definition's \"correctResponsesPattern\" property is an array of Strings": {"name": "An Activity Definition's \"correctResponsesPattern\" property is an array of Strings", "1.0.2_ref_text": "*******.table3.row2.a", "1.0.2_ref": "*******.table3.row2.a", "1.0.3_ref": "Data *******.s8.table1.row2", "1.0.3_link": ["Data.md#*******.s8.table1.row2"]}, "statement activity \"correctResponsesPattern\" is an array of strings": {"name": "statement activity \"correctResponsesPattern\" is an array of strings"}, "statement activity \"correctResponsesPattern\" is an object": {"name": "statement activity \"correctResponsesPattern\" is an object"}, "statement activity \"correctResponsesPattern\" is an array of object": {"name": "statement activity \"correctResponsesPattern\" is an array of object"}, "statement activity \"correctResponsesPattern\" is an array of number": {"name": "statement activity \"correctResponsesPattern\" is an array of number"}, "statement substatement activity \"correctResponsesPattern\" is an array of strings": {"name": "statement substatement activity \"correctResponsesPattern\" is an array of strings"}, "statement substatement activity \"correctResponsesPattern\" is an object": {"name": "statement substatement activity \"correctResponsesPattern\" is an object"}, "statement substatement activity \"correctResponsesPattern\" is an array of object": {"name": "statement substatement activity \"correctResponsesPattern\" is an array of object"}, "statement substatement activity \"correctResponsesPattern\" is an array of number": {"name": "statement substatement activity \"correctResponsesPattern\" is an array of number"}, "An Activity Definition's \"choices\" property is an array of Interaction Components": {"name": "An Activity Definition's \"choices\" property is an array of Interaction Components", "1.0.2_ref_text": "*******.table3.row3.a", "1.0.2_ref": "*******.table3.row3.a", "1.0.3_ref": "Data *******.s8.table1.row3", "1.0.3_link": ["Data.md#*******.s8.table1.row3"]}, "statement activity \"choices\" uses choice is an array of interaction components": {"name": "statement activity \"choices\" uses choice is an array of interaction components"}, "statement activity \"choices\" uses choice is not an array": {"name": "statement activity \"choices\" uses choice is not an array"}, "statement activity \"choices\" uses choice is an array of non string ID": {"name": "statement activity \"choices\" uses choice is an array of non string ID"}, "statement activity \"choices\" uses choice is an array of non object description": {"name": "statement activity \"choices\" uses choice is an array of non object description"}, "statement activity \"choices\" uses choice is an array of non description language": {"name": "statement activity \"choices\" uses choice is an array of non description language"}, "statement activity \"choices\" uses sequencing is an array of interaction components": {"name": "statement activity \"choices\" uses sequencing is an array of interaction components"}, "statement activity \"choices\" uses sequencing is not an array": {"name": "statement activity \"choices\" uses sequencing is not an array"}, "statement activity \"choices\" uses sequencing is an array of non string ID": {"name": "statement activity \"choices\" uses sequencing is an array of non string ID"}, "statement activity \"choices\" uses sequencing is an array of non object description": {"name": "statement activity \"choices\" uses sequencing is an array of non object description"}, "statement activity \"choices\" uses sequencing is an array of non description language": {"name": "statement activity \"choices\" uses sequencing is an array of non description language"}, "statement substatement activity \"choices\" uses choice is an array of interaction components": {"name": "statement substatement activity \"choices\" uses choice is an array of interaction components"}, "statement substatement activity \"choices\" uses choice is not an array": {"name": "statement substatement activity \"choices\" uses choice is not an array"}, "statement substatement activity \"choices\" uses choice is an array of non string ID": {"name": "statement substatement activity \"choices\" uses choice is an array of non string ID"}, "statement substatement activity \"choices\" uses choice is an array of non object description": {"name": "statement substatement activity \"choices\" uses choice is an array of non object description"}, "statement substatement activity \"choices\" uses choice is an array of non description language": {"name": "statement substatement activity \"choices\" uses choice is an array of non description language"}, "statement substatement activity \"choices\" uses sequencing is an array of interaction components": {"name": "statement substatement activity \"choices\" uses sequencing is an array of interaction components"}, "statement substatement activity \"choices\" uses sequencing is not an array": {"name": "statement substatement activity \"choices\" uses sequencing is not an array"}, "statement substatement activity \"choices\" uses sequencing is an array of non string ID": {"name": "statement substatement activity \"choices\" uses sequencing is an array of non string ID"}, "statement substatement activity \"choices\" uses sequencing is an array of non object description": {"name": "statement substatement activity \"choices\" uses sequencing is an array of non object description"}, "statement substatement activity \"choices\" uses sequencing is an array of non description language": {"name": "statement substatement activity \"choices\" uses sequencing is an array of non description language"}, "An Activity Definition's \"scale\" property is an array of Interaction Components": {"name": "An Activity Definition's \"scale\" property is an array of Interaction Components", "1.0.2_ref_text": "*******.table3.row3.a", "1.0.2_ref": "*******.table3.row3.a", "1.0.3_ref": "Data *******.s8.table1.row3", "1.0.3_link": ["Data.md#*******.s8.table1.row3"]}, "statement activity \"scale\" uses likert is an array of interaction components": {"name": "statement activity \"scale\" uses likert is an array of interaction components"}, "statement activity \"scale\" uses likert is not an array": {"name": "statement activity \"scale\" uses likert is not an array"}, "statement activity \"scale\" uses likert is an array of non string ID": {"name": "statement activity \"scale\" uses likert is an array of non string ID"}, "statement activity \"scale\" uses likert is an array of non object description": {"name": "statement activity \"scale\" uses likert is an array of non object description"}, "statement activity \"scale\" uses likert is an array of non description language": {"name": "statement activity \"scale\" uses likert is an array of non description language"}, "statement substatement activity \"scale\" uses likert is an array of interaction components": {"name": "statement substatement activity \"scale\" uses likert is an array of interaction components"}, "statement substatement activity \"scale\" uses likert is not an array": {"name": "statement substatement activity \"scale\" uses likert is not an array"}, "statement substatement activity \"scale\" uses likert is an array of non string ID": {"name": "statement substatement activity \"scale\" uses likert is an array of non string ID"}, "statement substatement activity \"scale\" uses likert is an array of non object description": {"name": "statement substatement activity \"scale\" uses likert is an array of non object description"}, "statement substatement activity \"scale\" uses likert is an array of non description language": {"name": "statement substatement activity \"scale\" uses likert is an array of non description language"}, "An Activity Definition's \"source\" property is an array of Interaction Components": {"name": "An Activity Definition's \"source\" property is an array of Interaction Components", "1.0.2_ref_text": "*******.table3.row3.a", "1.0.2_ref": "*******.table3.row3.a", "1.0.3_ref": "Data *******.s8.table1.row3", "1.0.3_link": ["Data.md#*******.s8.table1.row3"]}, "statement activity \"source\" uses matching is an array of interaction components": {"name": "statement activity \"source\" uses matching is an array of interaction components"}, "statement activity \"source\" uses matching is not an array": {"name": "statement activity \"source\" uses matching is not an array"}, "statement activity \"source\" uses matching is an array of non string ID": {"name": "statement activity \"source\" uses matching is an array of non string ID"}, "statement activity \"source\" uses matching is an array of non object description": {"name": "statement activity \"source\" uses matching is an array of non object description"}, "statement activity \"source\" uses matching is an array of non description language": {"name": "statement activity \"source\" uses matching is an array of non description language"}, "statement substatement activity \"source\" uses matching is an array of interaction components": {"name": "statement substatement activity \"source\" uses matching is an array of interaction components"}, "statement substatement activity \"source\" uses matching is not an array": {"name": "statement substatement activity \"source\" uses matching is not an array"}, "statement substatement activity \"source\" uses matching is an array of non string ID": {"name": "statement substatement activity \"source\" uses matching is an array of non string ID"}, "statement substatement activity \"source\" uses matching is an array of non object description": {"name": "statement substatement activity \"source\" uses matching is an array of non object description"}, "statement substatement activity \"source\" uses matching is an array of non description language": {"name": "statement substatement activity \"source\" uses matching is an array of non description language"}, "An Activity Definition's \"target\" property is an array of Interaction Components": {"name": "An Activity Definition's \"target\" property is an array of Interaction Components", "1.0.2_ref_text": "*******.table3.row3.a", "1.0.2_ref": "*******.table3.row3.a", "1.0.3_ref": "Data *******.s8.table1.row3", "1.0.3_link": ["Data.md#*******.s8.table1.row3"]}, "statement activity \"target\" uses matching is an array of interaction components": {"name": "statement activity \"target\" uses matching is an array of interaction components"}, "statement activity \"target\" uses matching is not an array": {"name": "statement activity \"target\" uses matching is not an array"}, "statement activity \"target\" uses matching is an array of non string ID": {"name": "statement activity \"target\" uses matching is an array of non string ID"}, "statement activity \"target\" uses matching is an array of non object description": {"name": "statement activity \"target\" uses matching is an array of non object description"}, "statement activity \"target\" uses matching is an array of non description language": {"name": "statement activity \"target\" uses matching is an array of non description language"}, "statement substatement activity \"target\" uses matching is an array of interaction components": {"name": "statement substatement activity \"target\" uses matching is an array of interaction components"}, "statement substatement activity \"target\" uses matching is not an array": {"name": "statement substatement activity \"target\" uses matching is not an array"}, "statement substatement activity \"target\" uses matching is an array of non string ID": {"name": "statement substatement activity \"target\" uses matching is an array of non string ID"}, "statement substatement activity \"target\" uses matching is an array of non object description": {"name": "statement substatement activity \"target\" uses matching is an array of non object description"}, "statement substatement activity \"target\" uses matching is an array of non description language": {"name": "statement substatement activity \"target\" uses matching is an array of non description language"}, "An Activity Definition's \"steps\" property is an array of Interaction Components": {"name": "An Activity Definition's \"steps\" property is an array of Interaction Components", "1.0.2_ref_text": "*******.table3.row3.a", "1.0.2_ref": "*******.table3.row3.a", "1.0.3_ref": "Data *******.s8.table1.row3", "1.0.3_link": ["Data.md#*******.s8.table1.row3"]}, "statement activity \"steps\" uses performance is an array of interaction components": {"name": "statement activity \"steps\" uses performance is an array of interaction components"}, "statement activity \"steps\" uses performance is not an array": {"name": "statement activity \"steps\" uses performance is not an array"}, "statement activity \"steps\" uses performance is an array of non string ID": {"name": "statement activity \"steps\" uses performance is an array of non string ID"}, "statement activity \"steps\" uses performance is an array of non object description": {"name": "statement activity \"steps\" uses performance is an array of non object description"}, "statement activity \"steps\" uses performance is an array of non description language": {"name": "statement activity \"steps\" uses performance is an array of non description language"}, "statement substatement activity \"steps\" uses performance is an array of interaction components": {"name": "statement substatement activity \"steps\" uses performance is an array of interaction components"}, "statement substatement activity \"steps\" uses performance is not an array": {"name": "statement substatement activity \"steps\" uses performance is not an array"}, "statement substatement activity \"steps\" uses performance is an array of non string ID": {"name": "statement substatement activity \"steps\" uses performance is an array of non string ID"}, "statement substatement activity \"steps\" uses performance is an array of non object description": {"name": "statement substatement activity \"steps\" uses performance is an array of non object description"}, "statement substatement activity \"steps\" uses performance is an array of non description language": {"name": "statement substatement activity \"steps\" uses performance is an array of non description language"}, "An Interaction Component is an Object": {"name": "An Interaction Component is an Object", "1.0.2_ref_text": "*******.table4", "1.0.2_ref": "*******.table4", "1.0.3_ref": "Data *******.s14", "1.0.3_link": ["Data.md#*******.s14"]}, "statement activity \"choice choices\" is not an object": {"name": "statement activity \"choice choices\" is not an object"}, "statement activity \"likert scale\" is not an object": {"name": "statement activity \"likert scale\" is not an object"}, "statement activity \"matching source\" is not an object": {"name": "statement activity \"matching source\" is not an object"}, "statement activity \"matching target\" is not an object": {"name": "statement activity \"matching target\" is not an object"}, "statement activity \"performance steps\" is not an object": {"name": "statement activity \"performance steps\" is not an object"}, "statement activity \"sequencing choices\" is not an object": {"name": "statement activity \"sequencing choices\" is not an object"}, "statement substatement activity \"choice choices\" is not an object": {"name": "statement substatement activity \"choice choices\" is not an object"}, "statement substatement activity \"likert scale\" is not an object": {"name": "statement substatement activity \"likert scale\" is not an object"}, "statement substatement activity \"matching source\" is not an object": {"name": "statement substatement activity \"matching source\" is not an object"}, "statement substatement activity \"matching target\" is not an object": {"name": "statement substatement activity \"matching target\" is not an object"}, "statement substatement activity \"performance steps\" is not an object": {"name": "statement substatement activity \"performance steps\" is not an object"}, "statement substatement activity \"sequencing choices\" is not an object": {"name": "statement substatement activity \"sequencing choices\" is not an object"}, "Interaction Component contains an \"id\" property": {"name": "Interaction Component contains an \"id\" property", "1.0.2_ref_text": "Multiplicity, *******.table4.row1.b", "1.0.2_ref": "*******.table4.row1.b", "1.0.3_ref": "Data *******.s15.table1.row1", "1.0.3_link": ["Data.md#*******.s15.table1.row1"]}, "statement activity \"choice choices\" missing \"id\"": {"name": "statement activity \"choice choices\" missing \"id\""}, "statement activity \"likert scale\" missing \"id\"": {"name": "statement activity \"likert scale\" missing \"id\""}, "statement activity \"matching source\" missing \"id\"": {"name": "statement activity \"matching source\" missing \"id\""}, "statement activity \"matching target\" missing \"id\"": {"name": "statement activity \"matching target\" missing \"id\""}, "statement activity \"performance steps\" missing \"id\"": {"name": "statement activity \"performance steps\" missing \"id\""}, "statement activity \"sequencing choices\" missing \"id\"": {"name": "statement activity \"sequencing choices\" missing \"id\""}, "statement substatement activity \"choice choices\" missing \"id\"": {"name": "statement substatement activity \"choice choices\" missing \"id\""}, "statement substatement activity \"likert scale\" missing \"id\"": {"name": "statement substatement activity \"likert scale\" missing \"id\""}, "statement substatement activity \"matching source\" missing \"id\"": {"name": "statement substatement activity \"matching source\" missing \"id\""}, "statement substatement activity \"matching target\" missing \"id\"": {"name": "statement substatement activity \"matching target\" missing \"id\""}, "statement substatement activity \"performance steps\" missing \"id\"": {"name": "statement substatement activity \"performance steps\" missing \"id\""}, "statement substatement activity \"sequencing choices\" missing \"id\"": {"name": "statement substatement activity \"sequencing choices\" missing \"id\""}, "An Interaction Component's \"id\" property is a String": {"name": "An Interaction Component's \"id\" property is a String", "1.0.2_ref_text": "Type, *******.table4.row1.a", "1.0.2_ref": "*******.table4.row1.a", "1.0.3_ref": "Data *******.s15.table1.row1", "1.0.3_link": ["Data.md#*******.s15.table1.row1"]}, "statement activity \"choice choices id\" not a string": {"name": "statement activity \"choice choices id\" not a string"}, "statement activity \"likert scale id\" not a string": {"name": "statement activity \"likert scale id\" not a string"}, "statement activity \"matching source id\" not a string": {"name": "statement activity \"matching source id\" not a string"}, "statement activity \"matching target id\" not a string": {"name": "statement activity \"matching target id\" not a string"}, "statement activity \"performance steps id\" not a string": {"name": "statement activity \"performance steps id\" not a string"}, "statement activity \"sequencing choices id\" not a string": {"name": "statement activity \"sequencing choices id\" not a string"}, "statement substatement activity \"choice choices id\" not a string": {"name": "statement substatement activity \"choice choices id\" not a string"}, "statement substatement activity \"likert scale id\" not a string": {"name": "statement substatement activity \"likert scale id\" not a string"}, "statement substatement activity \"matching source id\" not a string": {"name": "statement substatement activity \"matching source id\" not a string"}, "statement substatement activity \"matching target id\" not a string": {"name": "statement substatement activity \"matching target id\" not a string"}, "statement substatement activity \"performance steps id\" not a string": {"name": "statement substatement activity \"performance steps id\" not a string"}, "statement substatement activity \"sequencing choices id\" not a string": {"name": "statement substatement activity \"sequencing choices id\" not a string"}, "Within an array of Interaction Components, the \"id\" property is unique": {"name": "Within an array of Interaction Components, the \"id\" property is unique", "1.0.2_ref_text": "Multiplicty, *******.w", "1.0.2_ref": "*******.w", "1.0.3_ref": "Data *******.s16.b1", "1.0.3_link": ["Data.md#*******.s16.b1"]}, "statement activity choice \"choices\" cannot use same \"id\"": {"name": "statement activity choice \"choices\" cannot use same \"id\""}, "statement activity likert \"scale\" cannot use same \"id\"": {"name": "statement activity likert \"scale\" cannot use same \"id\""}, "statement activity matching \"source\" cannot use same \"id\"": {"name": "statement activity matching \"source\" cannot use same \"id\""}, "statement activity matching \"target\" cannot use same \"id\"": {"name": "statement activity matching \"target\" cannot use same \"id\""}, "statement activity performance \"steps\" cannot use same \"id\"": {"name": "statement activity performance \"steps\" cannot use same \"id\""}, "statement activity sequencing \"choices\" cannot use same \"id\"": {"name": "statement activity sequencing \"choices\" cannot use same \"id\""}, "statement substatement activity choice \"choices\" cannot use same \"id\"": {"name": "statement substatement activity choice \"choices\" cannot use same \"id\""}, "statement substatement activity likert \"scale\" cannot use same \"id\"": {"name": "statement substatement activity likert \"scale\" cannot use same \"id\""}, "statement substatement activity matching \"source\" cannot use same \"id\"": {"name": "statement substatement activity matching \"source\" cannot use same \"id\""}, "statement substatement activity matching \"target\" cannot use same \"id\"": {"name": "statement substatement activity matching \"target\" cannot use same \"id\""}, "statement substatement activity performance \"steps\" cannot use same \"id\"": {"name": "statement substatement activity performance \"steps\" cannot use same \"id\""}, "statement substatement activity sequencing \"choices\" cannot use same \"id\"": {"name": "statement substatement activity sequencing \"choices\" cannot use same \"id\""}, "An Activity Definition's \"extension\" property is an Object": {"name": "An Activity Definition's \"extension\" property is an Object", "1.0.2_ref_text": "Type, *******.table2.row1.a", "1.0.2_ref": "*******.table2.row1.a", "1.0.3_ref": "Data *******.s2.table1.row5", "1.0.3_link": ["Data.md#*******.s2.table1.row5"]}, "statement activity \"extension\" invalid string": {"name": "statement activity \"extension\" invalid string"}, "statement substatement activity \"extension\" invalid string": {"name": "statement substatement activity \"extension\" invalid string"}, "An LRS generates an \"objectType\" property of \"Activity\" to any \"object\" property if none is provided": {"name": "An LRS generates an \"objectType\" property of \"Activity\" to any \"object\" property if none is provided", "1.0.2_ref_text": "Modify, 4.1.4.a", "1.0.2_ref": "4.1.4.a", "1.0.3_ref": "Data 2.4.4.s2", "1.0.3_link": ["Data.md#2.4.4.s2"]}, "An LRS generates the \"id\" property of a Statement if none is provided": {"name": "An LRS generates the \"id\" property of a Statement if none is provided", "1.0.2_ref_text": "Modify, 4.1.4.a", "1.0.2_ref": "4.1.4.a", "1.0.3_ref": "Data 2.4.1.s2.b1", "1.0.3_link": ["Data.md#2.4.1.s2.b1"]}, "statement activity without \"objectType\" is valid": {"name": "statement activity without \"objectType\" is valid"}, "statement substatement activity without \"objectType\" is valid": {"name": "statement substatement activity without \"objectType\" is valid"}, "An \"objectType\" property is a String": {"name": "An \"objectType\" property is a String", "1.0.2_ref_text": "Type, *******.table1.row1.a", "1.0.2_ref": "*******.table1.row1.a", "1.0.3_ref": "Data *******.s2.table1.row1", "1.0.3_link": ["Data.md#*******.s2.table1.row1"]}, "statement actor \"objectType\" should fail numeric": {"name": "statement actor \"objectType\" should fail numeric"}, "statement actor \"objectType\" should fail object": {"name": "statement actor \"objectType\" should fail object"}, "statement authority \"objectType\" should fail numeric": {"name": "statement authority \"objectType\" should fail numeric"}, "statement authority \"objectType\" should fail object": {"name": "statement authority \"objectType\" should fail object"}, "statement context instructor \"objectType\" should fail numeric": {"name": "statement context instructor \"objectType\" should fail numeric"}, "statement context instructor \"objectType\" should fail object": {"name": "statement context instructor \"objectType\" should fail object"}, "statement substatement as agent with \"objectType\" should fail numeric": {"name": "statement substatement as agent with \"objectType\" should fail numeric"}, "statement substatement as agent with \"objectType\" should fail object": {"name": "statement substatement as agent with \"objectType\" should fail object"}, "statement substatement\"s agent \"objectType\" should fail numeric": {"name": "statement substatement\"s agent \"objectType\" should fail numeric"}, "statement substatement\"s agent \"objectType\" should fail object": {"name": "statement substatement\"s agent \"objectType\" should fail object"}, "statement substatement\"s context instructor \"objectType\" should fail numeric": {"name": "statement substatement\"s context instructor \"objectType\" should fail numeric"}, "statement substatement\"s context instructor \"objectType\" should fail object": {"name": "statement substatement\"s context instructor \"objectType\" should fail object"}, "A \"name\" property is a String": {"name": "A \"name\" property is a String", "1.0.2_ref_text": "Type, *******.table1.row2.a", "1.0.2_ref": "*******.table1.row2.a", "1.0.3_ref": "Data *******.s2.table1.row2", "1.0.3_link": ["Data.md#*******.s2.table1.row2"]}, "statement actor \"name\" should fail numeric": {"name": "statement actor \"name\" should fail numeric"}, "statement actor \"name\" should fail object": {"name": "statement actor \"name\" should fail object"}, "statement authority \"name\" should fail numeric": {"name": "statement authority \"name\" should fail numeric"}, "statement authority \"name\" should fail object": {"name": "statement authority \"name\" should fail object"}, "statement context instructor \"name\" should fail numeric": {"name": "statement context instructor \"name\" should fail numeric"}, "statement context instructor \"name\" should fail object": {"name": "statement context instructor \"name\" should fail object"}, "statement substatement as agent with \"name\" should fail numeric": {"name": "statement substatement as agent with \"name\" should fail numeric"}, "statement substatement as agent with \"name\" should fail object": {"name": "statement substatement as agent with \"name\" should fail object"}, "statement substatement\"s agent \"name\" should fail numeric": {"name": "statement substatement\"s agent \"name\" should fail numeric"}, "statement substatement\"s agent \"name\" should fail object": {"name": "statement substatement\"s agent \"name\" should fail object"}, "statement substatement\"s context instructor \"name\" should fail numeric": {"name": "statement substatement\"s context instructor \"name\" should fail numeric"}, "statement substatement\"s context instructor \"name\" should fail object": {"name": "statement substatement\"s context instructor \"name\" should fail object"}, "An \"actor\" property with \"objectType\" as \"Agent\" uses one of the following properties: \"mbox\", \"mbox_sha1sum\", \"openid\", \"account\"": {"name": "An \"actor\" property with \"objectType\" as \"Agent\" uses one of the following properties: \"mbox\", \"mbox_sha1sum\", \"openid\", \"account\"", "1.0.2_ref_text": "Multiplicity, *******.a", "1.0.2_ref": "*******.a", "1.0.3_ref": "Data *******.s2.b1", "1.0.3_link": ["Data.md#*******.s2.b1"]}, "statement actor without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail": {"name": "statement actor without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail"}, "statement actor \"account\" should pass": {"name": "statement actor \"account\" should pass"}, "statement actor \"mbox\" should pass": {"name": "statement actor \"mbox\" should pass"}, "statement actor \"mbox_sha1sum\" should pass": {"name": "statement actor \"mbox_sha1sum\" should pass"}, "statement actor \"openid\" should pass": {"name": "statement actor \"openid\" should pass"}, "statement authority without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail": {"name": "statement authority without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail"}, "statement authority \"account\" should pass": {"name": "statement authority \"account\" should pass"}, "statement authority \"mbox\" should pass": {"name": "statement authority \"mbox\" should pass"}, "statement authority \"mbox_sha1sum\" should pass": {"name": "statement authority \"mbox_sha1sum\" should pass"}, "statement authority \"openid\" should pass": {"name": "statement authority \"openid\" should pass"}, "statement context instructor without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail": {"name": "statement context instructor without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail"}, "statement context instructor \"account\" should pass": {"name": "statement context instructor \"account\" should pass"}, "statement context instructor \"mbox\" should pass": {"name": "statement context instructor \"mbox\" should pass"}, "statement context instructor \"mbox_sha1sum\" should pass": {"name": "statement context instructor \"mbox_sha1sum\" should pass"}, "statement context instructor \"openid\" should pass": {"name": "statement context instructor \"openid\" should pass"}, "statement substatement as agent without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail": {"name": "statement substatement as agent without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail"}, "statement substatement as agent \"account\" should pass": {"name": "statement substatement as agent \"account\" should pass"}, "statement substatement as agent \"mbox\" should pass": {"name": "statement substatement as agent \"mbox\" should pass"}, "statement substatement as agent \"mbox_sha1sum\" should pass": {"name": "statement substatement as agent \"mbox_sha1sum\" should pass"}, "statement substatement as agent \"openid\" should pass": {"name": "statement substatement as agent \"openid\" should pass"}, "statement substatement\"s agent without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail": {"name": "statement substatement\"s agent without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail"}, "statement substatement\"s agent \"account\" should pass": {"name": "statement substatement\"s agent \"account\" should pass"}, "statement substatement\"s agent \"mbox\" should pass": {"name": "statement substatement\"s agent \"mbox\" should pass"}, "statement substatement\"s agent \"mbox_sha1sum\" should pass": {"name": "statement substatement\"s agent \"mbox_sha1sum\" should pass"}, "statement substatement\"s agent \"openid\" should pass": {"name": "statement substatement\"s agent \"openid\" should pass"}, "statement substatement\"s context instructor without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail": {"name": "statement substatement\"s context instructor without \"account\", \"mbox\", \"mbox_sha1sum\", \"openid\" should fail"}, "statement substatement\"s context instructor \"account\" should pass": {"name": "statement substatement\"s context instructor \"account\" should pass"}, "statement substatement\"s context instructor \"mbox\" should pass": {"name": "statement substatement\"s context instructor \"mbox\" should pass"}, "statement substatement\"s context instructor \"mbox_sha1sum\" should pass": {"name": "statement substatement\"s context instructor \"mbox_sha1sum\" should pass"}, "statement substatement\"s context instructor \"openid\" should pass": {"name": "statement substatement\"s context instructor \"openid\" should pass"}, "An \"mbox\" property is an IRI": {"name": "An \"mbox\" property is an IRI", "1.0.2_ref_text": "Type, *******.table1.row1.a", "1.0.2_ref": "*******.table1.row1.a", "1.0.3_ref": "Data *******.s3.table1.row1", "1.0.3_link": ["Data.md#*******.s3.table1.row1"]}, "statement actor \"agent mbox\" not IRI": {"name": "statement actor \"agent mbox\" not IRI"}, "statement actor \"group mbox\" not IRI": {"name": "statement actor \"group mbox\" not IRI"}, "statement authority \"agent mbox\" not IRI": {"name": "statement authority \"agent mbox\" not IRI"}, "statement authority \"group mbox\" not IRI": {"name": "statement authority \"group mbox\" not IRI"}, "statement context instructor \"agent mbox\" not IRI": {"name": "statement context instructor \"agent mbox\" not IRI"}, "statement context instructor \"group mbox\" not IRI": {"name": "statement context instructor \"group mbox\" not IRI"}, "statement context team \"group mbox\" not IRI": {"name": "statement context team \"group mbox\" not IRI"}, "statement substatement as \"agent mbox\" not IRI": {"name": "statement substatement as \"agent mbox\" not IRI"}, "statement substatement as \"group mbox\" not IRI": {"name": "statement substatement as \"group mbox\" not IRI"}, "statement substatement\"s \"agent mbox\" not IRI": {"name": "statement substatement\"s \"agent mbox\" not IRI"}, "statement substatement\"s \"group mbox\" not IRI": {"name": "statement substatement\"s \"group mbox\" not IRI"}, "statement substatement\"s context instructor \"agent mbox\" not IRI": {"name": "statement substatement\"s context instructor \"agent mbox\" not IRI"}, "statement substatement\"s context instructor \"group mbox\" not IRI": {"name": "statement substatement\"s context instructor \"group mbox\" not IRI"}, "statement substatement\"s context team \"group mbox\" not IRI": {"name": "statement substatement\"s context team \"group mbox\" not IRI"}, "An \"mbox\" property has the form \"mailto:email address\"": {"name": "An \"mbox\" property has the form \"mailto:email address\"", "1.0.2_ref_text": "<PERSON>ynta<PERSON>, *******.table1.row1.b", "1.0.2_ref": "*******.table1.row1.b", "1.0.3_ref": "Data *******.s3.table1.row1", "1.0.3_link": ["Data.md#*******.s3.table1.row1"]}, "statement actor \"agent mbox\" not mailto:email address": {"name": "statement actor \"agent mbox\" not mailto:email address"}, "statement actor \"group mbox\" not mailto:email address": {"name": "statement actor \"group mbox\" not mailto:email address"}, "statement authority \"agent mbox\" not mailto:email address": {"name": "statement authority \"agent mbox\" not mailto:email address"}, "statement authority \"group mbox\" not mailto:email address": {"name": "statement authority \"group mbox\" not mailto:email address"}, "statement context instructor \"agent mbox\" not mailto:email address": {"name": "statement context instructor \"agent mbox\" not mailto:email address"}, "statement context instructor \"group mbox\" not mailto:email address": {"name": "statement context instructor \"group mbox\" not mailto:email address"}, "statement context team \"group mbox\" not mailto:email address": {"name": "statement context team \"group mbox\" not mailto:email address"}, "statement substatement as \"agent mbox\" not mailto:email address": {"name": "statement substatement as \"agent mbox\" not mailto:email address"}, "statement substatement as \"group mbox\" not mailto:email address": {"name": "statement substatement as \"group mbox\" not mailto:email address"}, "statement substatement\"s \"agent mbox\" not mailto:email address": {"name": "statement substatement\"s \"agent mbox\" not mailto:email address"}, "statement substatement\"s \"group mbox\" not mailto:email address": {"name": "statement substatement\"s \"group mbox\" not mailto:email address"}, "statement substatement\"s context instructor \"agent mbox\" not mailto:email address": {"name": "statement substatement\"s context instructor \"agent mbox\" not mailto:email address"}, "statement substatement\"s context instructor \"group mbox\" not mailto:email address": {"name": "statement substatement\"s context instructor \"group mbox\" not mailto:email address"}, "statement substatement\"s context team \"group mbox\" not mailto:email address": {"name": "statement substatement\"s context team \"group mbox\" not mailto:email address"}, "An \"mbox_sha1sum\" property is a String": {"name": "An \"mbox_sha1sum\" property is a String", "1.0.2_ref_text": "Type, *******.table1.row2.a", "1.0.2_ref": "*******.table1.row2.a", "1.0.3_ref": "Data *******.s3.table1.row2", "1.0.3_link": ["Data.md#*******.s3.table1.row2"]}, "statement actor \"agent mbox_sha1sum\" not string": {"name": "statement actor \"agent mbox_sha1sum\" not string"}, "statement actor \"group mbox_sha1sum\" not string": {"name": "statement actor \"group mbox_sha1sum\" not string"}, "statement authority \"agent mbox_sha1sum\" not string": {"name": "statement authority \"agent mbox_sha1sum\" not string"}, "statement authority \"group mbox_sha1sum\" not string": {"name": "statement authority \"group mbox_sha1sum\" not string"}, "statement context instructor \"agent mbox_sha1sum\" not string": {"name": "statement context instructor \"agent mbox_sha1sum\" not string"}, "statement context instructor \"group mbox_sha1sum\" not string": {"name": "statement context instructor \"group mbox_sha1sum\" not string"}, "statement context team \"group mbox_sha1sum\" not string": {"name": "statement context team \"group mbox_sha1sum\" not string"}, "statement substatement as \"agent mbox_sha1sum\" not string": {"name": "statement substatement as \"agent mbox_sha1sum\" not string"}, "statement substatement as \"group mbox_sha1sum\" not string": {"name": "statement substatement as \"group mbox_sha1sum\" not string"}, "statement substatement\"s \"agent mbox_sha1sum\" not string": {"name": "statement substatement\"s \"agent mbox_sha1sum\" not string"}, "statement substatement\"s \"group mbox_sha1sum\" not string": {"name": "statement substatement\"s \"group mbox_sha1sum\" not string"}, "statement substatement\"s context instructor \"agent mbox_sha1sum\" not string": {"name": "statement substatement\"s context instructor \"agent mbox_sha1sum\" not string"}, "statement substatement\"s context instructor \"group mbox_sha1sum\" not string": {"name": "statement substatement\"s context instructor \"group mbox_sha1sum\" not string"}, "statement substatement\"s context team \"group mbox_sha1sum\" not string": {"name": "statement substatement\"s context team \"group mbox_sha1sum\" not string"}, "An \"openid\" property is a URI": {"name": "An \"openid\" property is a URI", "1.0.2_ref_text": "Type, *******.table1.row3.a", "1.0.2_ref": "*******.table1.row3.a", "1.0.3_ref": "Data *******.s3.table1.row3", "1.0.3_link": ["Data.md#*******.s3.table1.row3"]}, "statement actor \"agent openid\" not URI": {"name": "statement actor \"agent openid\" not URI"}, "statement actor \"group openid\" not URI": {"name": "statement actor \"group openid\" not URI"}, "statement authority \"agent openid\" not URI": {"name": "statement authority \"agent openid\" not URI"}, "statement authority \"group openid\" not URI": {"name": "statement authority \"group openid\" not URI"}, "statement context instructor \"agent openid\" not URI": {"name": "statement context instructor \"agent openid\" not URI"}, "statement context instructor \"group openid\" not URI": {"name": "statement context instructor \"group openid\" not URI"}, "statement context team \"group openid\" not URI": {"name": "statement context team \"group openid\" not URI"}, "statement substatement as \"agent openid\" not URI": {"name": "statement substatement as \"agent openid\" not URI"}, "statement substatement as \"group openid\" not URI": {"name": "statement substatement as \"group openid\" not URI"}, "statement substatement\"s \"agent openid\" not URI": {"name": "statement substatement\"s \"agent openid\" not URI"}, "statement substatement\"s \"group openid\" not URI": {"name": "statement substatement\"s \"group openid\" not URI"}, "statement substatement\"s context instructor \"agent openid\" not URI": {"name": "statement substatement\"s context instructor \"agent openid\" not URI"}, "statement substatement\"s context instructor \"group openid\" not URI": {"name": "statement substatement\"s context instructor \"group openid\" not URI"}, "statement substatement\"s context team \"group openid\" not URI": {"name": "statement substatement\"s context team \"group openid\" not URI"}, "An Account Object is the \"account\" property of a Group or Agent": {"name": "An Account Object is the \"account\" property of a Group or Agent", "1.0.2_ref_text": "Definition, *******", "1.0.2_ref": "*******", "1.0.3_ref": "Data *******", "1.0.3_link": ["Data.md#*******"]}, "statement actor \"agent account\" property exists": {"name": "statement actor \"agent account\" property exists"}, "statement actor \"group account\" property exists": {"name": "statement actor \"group account\" property exists"}, "statement authority \"agent account\" property exists": {"name": "statement authority \"agent account\" property exists"}, "statement authority \"group account\" property exists": {"name": "statement authority \"group account\" property exists"}, "statement context instructor \"agent account\" property exists": {"name": "statement context instructor \"agent account\" property exists"}, "statement context instructor \"group account\" property exists": {"name": "statement context instructor \"group account\" property exists"}, "statement context team \"group account\" property exists": {"name": "statement context team \"group account\" property exists"}, "statement substatement as \"agent account\" property exists": {"name": "statement substatement as \"agent account\" property exists"}, "statement substatement as \"group account\" property exists": {"name": "statement substatement as \"group account\" property exists"}, "statement substatement\"s \"agent account\" property exists": {"name": "statement substatement\"s \"agent account\" property exists"}, "statement substatement\"s \"group account\" property exists": {"name": "statement substatement\"s \"group account\" property exists"}, "statement substatement\"s context instructor \"agent account\" property exists": {"name": "statement substatement\"s context instructor \"agent account\" property exists"}, "statement substatement\"s context instructor \"group account\" property exists": {"name": "statement substatement\"s context instructor \"group account\" property exists"}, "statement substatement\"s context team \"group account\" property exists": {"name": "statement substatement\"s context team \"group account\" property exists"}, "An Account Object uses the \"homePage\" property": {"name": "An Account Object uses the \"homePage\" property", "1.0.2_ref_text": "Multiplicity, *******.table1.row1.b", "1.0.2_ref": "*******.table1.row1.b", "1.0.3_ref": "Data *******.s2.table1.row1", "1.0.3_link": ["Data.md#*******.s2.table1.row1"]}, "statement actor \"agent\" account \"homePage\" property exists": {"name": "statement actor \"agent\" account \"homePage\" property exists"}, "statement actor \"group\" account \"homePage\" property exists": {"name": "statement actor \"group\" account \"homePage\" property exists"}, "statement authority \"agent\" account \"homePage\" property exists": {"name": "statement authority \"agent\" account \"homePage\" property exists"}, "statement authority \"group\" account \"homePage\" property exists": {"name": "statement authority \"group\" account \"homePage\" property exists"}, "statement context instructor \"agent\" account \"homePage\" property exists": {"name": "statement context instructor \"agent\" account \"homePage\" property exists"}, "statement context instructor \"group\" account \"homePage\" property exists": {"name": "statement context instructor \"group\" account \"homePage\" property exists"}, "statement context team \"group\" account \"homePage\" property exists": {"name": "statement context team \"group\" account \"homePage\" property exists"}, "statement substatement as \"agent\" account \"homePage\" property exists": {"name": "statement substatement as \"agent\" account \"homePage\" property exists"}, "statement substatement as \"group\" account \"homePage\" property exists": {"name": "statement substatement as \"group\" account \"homePage\" property exists"}, "statement substatement\"s \"agent\" account \"homePage\" property exists": {"name": "statement substatement\"s \"agent\" account \"homePage\" property exists"}, "statement substatement\"s \"group\" account \"homePage\" property exists": {"name": "statement substatement\"s \"group\" account \"homePage\" property exists"}, "statement substatement\"s context instructor \"agent\" account \"homePage\" property exists": {"name": "statement substatement\"s context instructor \"agent\" account \"homePage\" property exists"}, "statement substatement\"s context instructor \"group\" account \"homePage\" property exists": {"name": "statement substatement\"s context instructor \"group\" account \"homePage\" property exists"}, "statement substatement\"s context team \"group\" account \"homePage\" property exists": {"name": "statement substatement\"s context team \"group\" account \"homePage\" property exists"}, "An Account Object's \"homePage\" property is an IRL": {"name": "An Account <PERSON>bject's \"homePage\" property is an IRL", "1.0.2_ref_text": "Type, *******.table1.row1.a", "1.0.2_ref": "*******.table1.row1.a", "1.0.3_ref": "Data *******.s2.table1.row1", "1.0.3_link": ["Data.md#*******.s2.table1.row1"]}, "statement actor \"agent\" account \"homePage property is IRL": {"name": "statement actor \"agent\" account \"homePage property is IRL"}, "statement actor \"group\" account \"homePage property is IRL": {"name": "statement actor \"group\" account \"homePage property is IRL"}, "statement authority \"agent\" account \"homePage property is IRL": {"name": "statement authority \"agent\" account \"homePage property is IRL"}, "statement authority \"group\" account \"homePage property is IRL": {"name": "statement authority \"group\" account \"homePage property is IRL"}, "statement context instructor \"agent\" account \"homePage property is IRL": {"name": "statement context instructor \"agent\" account \"homePage property is IRL"}, "statement context instructor \"group\" account \"homePage property is IRL": {"name": "statement context instructor \"group\" account \"homePage property is IRL"}, "statement context team \"group\" account \"homePage property is IRL": {"name": "statement context team \"group\" account \"homePage property is IRL"}, "statement substatement as \"agent\" account \"homePage property is IRL": {"name": "statement substatement as \"agent\" account \"homePage property is IRL"}, "statement substatement as \"group\" account \"homePage property is IRL": {"name": "statement substatement as \"group\" account \"homePage property is IRL"}, "statement substatement\"s \"agent\" account \"homePage property is IRL": {"name": "statement substatement\"s \"agent\" account \"homePage property is IRL"}, "statement substatement\"s \"group\" account \"homePage property is IRL": {"name": "statement substatement\"s \"group\" account \"homePage property is IRL"}, "statement substatement\"s context instructor \"agent\" account \"homePage property is IRL": {"name": "statement substatement\"s context instructor \"agent\" account \"homePage property is IRL"}, "statement substatement\"s context instructor \"group\" account \"homePage property is IRL": {"name": "statement substatement\"s context instructor \"group\" account \"homePage property is IRL"}, "statement substatement\"s context team \"group\" account \"homePage property is IRL": {"name": "statement substatement\"s context team \"group\" account \"homePage property is IRL"}, "An Account Object uses the \"name\" property": {"name": "An Account Object uses the \"name\" property", "1.0.2_ref_text": "Multiplicity, *******.table1.row2.b", "1.0.2_ref": "*******.table1.row2.b", "1.0.3_ref": "Data *******.s2.table1.row2", "1.0.3_link": ["Data.md#*******.s2.table1.row2"]}, "statement actor \"agent\" account \"name\" property exists": {"name": "statement actor \"agent\" account \"name\" property exists"}, "statement actor \"group\" account \"name\" property exists": {"name": "statement actor \"group\" account \"name\" property exists"}, "statement authority \"agent\" account \"name\" property exists": {"name": "statement authority \"agent\" account \"name\" property exists"}, "statement authority \"group\" account \"name\" property exists": {"name": "statement authority \"group\" account \"name\" property exists"}, "statement context instructor \"agent\" account \"name\" property exists": {"name": "statement context instructor \"agent\" account \"name\" property exists"}, "statement context instructor \"group\" account \"name\" property exists": {"name": "statement context instructor \"group\" account \"name\" property exists"}, "statement context team \"group\" account \"name\" property exists": {"name": "statement context team \"group\" account \"name\" property exists"}, "statement substatement as \"agent\" account \"name\" property exists": {"name": "statement substatement as \"agent\" account \"name\" property exists"}, "statement substatement as \"group\" account \"name\" property exists": {"name": "statement substatement as \"group\" account \"name\" property exists"}, "statement substatement\"s \"agent\" account \"name\" property exists": {"name": "statement substatement\"s \"agent\" account \"name\" property exists"}, "statement substatement\"s \"group\" account \"name\" property exists": {"name": "statement substatement\"s \"group\" account \"name\" property exists"}, "statement substatement\"s context instructor \"agent\" account \"name\" property exists": {"name": "statement substatement\"s context instructor \"agent\" account \"name\" property exists"}, "statement substatement\"s context instructor \"group\" account \"name\" property exists": {"name": "statement substatement\"s context instructor \"group\" account \"name\" property exists"}, "statement substatement\"s context team \"group\" account \"name\" property exists": {"name": "statement substatement\"s context team \"group\" account \"name\" property exists"}, "An Account Object \"name\" property is a String": {"name": "An Account Object \"name\" property is a String", "1.0.2_ref_text": "Type, *******.table1.row1.a", "1.0.2_ref": "*******.table1.row2.a", "1.0.3_ref": "Data *******.s2.table1.row2", "1.0.3_link": ["Data.md#*******.s2.table1.row2"]}, "statement actor \"agent\" account \"name\" property is string": {"name": "statement actor \"agent\" account \"name\" property is string"}, "statement actor \"group\" account \"name\" property is string": {"name": "statement actor \"group\" account \"name\" property is string"}, "statement authority \"agent\" account \"name\" property is string": {"name": "statement authority \"agent\" account \"name\" property is string"}, "statement authority \"group\" account \"name\" property is string": {"name": "statement authority \"group\" account \"name\" property is string"}, "statement context instructor \"agent\" account \"name\" property is string": {"name": "statement context instructor \"agent\" account \"name\" property is string"}, "statement context instructor \"group\" account \"name\" property is string": {"name": "statement context instructor \"group\" account \"name\" property is string"}, "statement context team \"group\" account \"name\" property is string": {"name": "statement context team \"group\" account \"name\" property is string"}, "statement substatement as \"agent\" account \"name\" property is string": {"name": "statement substatement as \"agent\" account \"name\" property is string"}, "statement substatement as \"group\" account \"name\" property is string": {"name": "statement substatement as \"group\" account \"name\" property is string"}, "statement substatement\"s \"agent\" account \"name\" property is string": {"name": "statement substatement\"s \"agent\" account \"name\" property is string"}, "statement substatement\"s \"group\" account \"name\" property is string": {"name": "statement substatement\"s \"group\" account \"name\" property is string"}, "statement substatement\"s context instructor \"agent\" account \"name\" property is string": {"name": "statement substatement\"s context instructor \"agent\" account \"name\" property is string"}, "statement substatement\"s context instructor \"group\" account \"name\" property is string": {"name": "statement substatement\"s context instructor \"group\" account \"name\" property is string"}, "statement substatement\"s context team \"group\" account \"name\" property is string": {"name": "statement substatement\"s context team \"group\" account \"name\" property is string"}, "Agents Verify Templates": {"name": "Agents Verify Templates"}, "should pass statement actor template": {"name": "should pass statement actor template"}, "should pass statement authority template": {"name": "should pass statement authority template"}, "should pass statement context instructor template": {"name": "should pass statement context instructor template"}, "should pass statement substatement as agent template": {"name": "should pass statement substatement as agent template"}, "should pass statement substatement\"s agent template": {"name": "should pass statement substatement\"s agent template"}, "should pass statement substatement\"s context instructor template": {"name": "should pass statement substatement\"s context instructor template"}, "An \"actor\" property's \"objectType\" property is either \"Agent\" or \"Group\"": {"name": "An \"actor\" property's \"objectType\" property is either \"Agent\" or \"Group\"", "1.0.2_ref_text": "Vocabulary, *******.table1.row1.b, *******.table1.row1.b", "1.0.2_ref": "*******.table1.row1.b, *******.table1.row1.b, *******.table2.row1.b", "1.0.3_ref": "Data *******, Data *******", "1.0.3_link": ["Data.md#*******", "Data.md#*******"]}, "statement actor \"objectType\" should fail when not \"Agent\"": {"name": "statement actor \"objectType\" should fail when not \"Agent\""}, "statement actor \"objectType\" should fail when not \"Group\"": {"name": "statement actor \"objectType\" should fail when not \"Group\""}, "statement authority \"objectType\" should fail when not \"Agent\"": {"name": "statement authority \"objectType\" should fail when not \"Agent\""}, "statement authority \"objectType\" should fail when not \"Group\"": {"name": "statement authority \"objectType\" should fail when not \"Group\""}, "statement context instructor \"objectType\" should fail when not \"Agent\"": {"name": "statement context instructor \"objectType\" should fail when not \"Agent\""}, "statement context instructor \"objectType\" should fail when not \"Group\"": {"name": "statement context instructor \"objectType\" should fail when not \"Group\""}, "statement substatement as agent with \"objectType\" should fail when not \"Agent\"": {"name": "statement substatement as agent with \"objectType\" should fail when not \"Agent\""}, "statement substatement as group with \"objectType\" should fail when not \"Group\"": {"name": "statement substatement as group with \"objectType\" should fail when not \"Group\""}, "statement substatement\"s actor \"objectType\" should fail when not \"Agent\"": {"name": "statement substatement\"s actor \"objectType\" should fail when not \"Agent\""}, "statement substatement\"s actor \"objectType\" should fail when not \"Group\"": {"name": "statement substatement\"s actor \"objectType\" should fail when not \"Group\""}, "statement substatement\"s context instructor \"objectType\" should fail when not \"Agent\"": {"name": "statement substatement\"s context instructor \"objectType\" should fail when not \"Agent\""}, "statement substatement\"s context instructor \"objectType\" should fail when not \"Group\"": {"name": "statement substatement\"s context instructor \"objectType\" should fail when not \"Group\""}, "An Agent is defined by \"objectType\" of an \"actor\" property or \"object\" property with value \"Agent\"": {"name": "An Agent is defined by \"objectType\" of an \"actor\" property or \"object\" property with value \"Agent\"", "1.0.2_ref_text": "*******.table1.row1, *******.a", "1.0.2_ref": "*******.table1.row1, *******.a", "1.0.3_ref": "Data *******.s2.table1.row1", "1.0.3_link": ["Data.md#*******.s2.table1.row1"]}, "statement actor does not require objectType": {"name": "statement actor does not require objectType"}, "statement actor \"objectType\" accepts \"Agent\"": {"name": "statement actor \"objectType\" accepts \"Agent\""}, "statement authority \"objectType\" accepts \"Agent\"": {"name": "statement authority \"objectType\" accepts \"Agent\""}, "statement context instructor \"objectType\" accepts \"Agent\"": {"name": "statement context instructor \"objectType\" accepts \"Agent\""}, "statement substatement as agent \"objectType\" accepts \"Agent\"": {"name": "statement substatement as agent \"objectType\" accepts \"Agent\""}, "statement substatement\"s agent \"objectType\" accepts \"Agent\"": {"name": "statement substatement\"s agent \"objectType\" accepts \"Agent\""}, "statement substatement\"s context instructor \"objectType\" accepts \"Agent\"": {"name": "statement substatement\"s context instructor \"objectType\" accepts \"Agent\""}, "An Agent does not use the \"mbox\" property if \"mbox_sha1sum\", \"openid\", or \"account\" are used": {"name": "An Agent does not use the \"mbox\" property if \"mbox_sha1sum\", \"openid\", or \"account\" are used", "1.0.2_ref_text": "Multiplicity, *******.b", "1.0.2_ref": "*******.b", "1.0.3_ref": "Data *******.s2.b2", "1.0.3_link": ["Data.md#*******.s2.b2"]}, "statement actor \"mbox\" cannot be used with \"account\"": {"name": "statement actor \"mbox\" cannot be used with \"account\""}, "statement actor \"mbox\" cannot be used with \"mbox_sha1sum\"": {"name": "statement actor \"mbox\" cannot be used with \"mbox_sha1sum\""}, "statement actor \"mbox\" cannot be used with \"openid\"": {"name": "statement actor \"mbox\" cannot be used with \"openid\""}, "statement authority \"mbox\" cannot be used with \"account\"": {"name": "statement authority \"mbox\" cannot be used with \"account\""}, "statement authority \"mbox\" cannot be used with \"mbox_sha1sum\"": {"name": "statement authority \"mbox\" cannot be used with \"mbox_sha1sum\""}, "statement authority \"mbox\" cannot be used with \"openid\"": {"name": "statement authority \"mbox\" cannot be used with \"openid\""}, "statement context instructor \"mbox\" cannot be used with \"account\"": {"name": "statement context instructor \"mbox\" cannot be used with \"account\""}, "statement context instructor \"mbox\" cannot be used with \"mbox_sha1sum\"": {"name": "statement context instructor \"mbox\" cannot be used with \"mbox_sha1sum\""}, "statement context instructor \"mbox\" cannot be used with \"openid\"": {"name": "statement context instructor \"mbox\" cannot be used with \"openid\""}, "statement substatement as agent \"mbox\" cannot be used with \"account\"": {"name": "statement substatement as agent \"mbox\" cannot be used with \"account\""}, "statement substatement as agent \"mbox\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement as agent \"mbox\" cannot be used with \"mbox_sha1sum\""}, "statement substatement as agent \"mbox\" cannot be used with \"openid\"": {"name": "statement substatement as agent \"mbox\" cannot be used with \"openid\""}, "statement substatement\"s agent \"mbox\" cannot be used with \"account\"": {"name": "statement substatement\"s agent \"mbox\" cannot be used with \"account\""}, "statement substatement\"s agent \"mbox\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement\"s agent \"mbox\" cannot be used with \"mbox_sha1sum\""}, "statement substatement\"s agent \"mbox\" cannot be used with \"openid\"": {"name": "statement substatement\"s agent \"mbox\" cannot be used with \"openid\""}, "statement substatement\"s context instructor \"mbox\" cannot be used with \"account\"": {"name": "statement substatement\"s context instructor \"mbox\" cannot be used with \"account\""}, "statement substatement\"s context instructor \"mbox\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement\"s context instructor \"mbox\" cannot be used with \"mbox_sha1sum\""}, "statement substatement\"s context instructor \"mbox\" cannot be used with \"openid\"": {"name": "statement substatement\"s context instructor \"mbox\" cannot be used with \"openid\""}, "An Agent does not use the \"mbox_sha1sum\" property if \"mbox\", \"openid\", or \"account\" are used": {"name": "An Agent does not use the \"mbox_sha1sum\" property if \"mbox\", \"openid\", or \"account\" are used", "1.0.2_ref_text": "Multiplicity, *******.b", "1.0.2_ref": "*******.b", "1.0.3_ref": "Data *******.s2.b2", "1.0.3_link": ["Data.md#*******.s2.b2"]}, "statement actor \"mbox_sha1sum\" cannot be used with \"account\"": {"name": "statement actor \"mbox_sha1sum\" cannot be used with \"account\""}, "statement actor \"mbox_sha1sum\" cannot be used with \"mbox\"": {"name": "statement actor \"mbox_sha1sum\" cannot be used with \"mbox\""}, "statement actor \"mbox_sha1sum\" cannot be used with \"openid\"": {"name": "statement actor \"mbox_sha1sum\" cannot be used with \"openid\""}, "statement authority \"mbox_sha1sum\" cannot be used with \"account\"": {"name": "statement authority \"mbox_sha1sum\" cannot be used with \"account\""}, "statement authority \"mbox_sha1sum\" cannot be used with \"mbox\"": {"name": "statement authority \"mbox_sha1sum\" cannot be used with \"mbox\""}, "statement authority \"mbox_sha1sum\" cannot be used with \"openid\"": {"name": "statement authority \"mbox_sha1sum\" cannot be used with \"openid\""}, "statement context instructor \"mbox_sha1sum\" cannot be used with \"account\"": {"name": "statement context instructor \"mbox_sha1sum\" cannot be used with \"account\""}, "statement context instructor \"mbox_sha1sum\" cannot be used with \"mbox\"": {"name": "statement context instructor \"mbox_sha1sum\" cannot be used with \"mbox\""}, "statement context instructor \"mbox_sha1sum\" cannot be used with \"openid\"": {"name": "statement context instructor \"mbox_sha1sum\" cannot be used with \"openid\""}, "statement substatement as agent \"mbox_sha1sum\" cannot be used with \"account\"": {"name": "statement substatement as agent \"mbox_sha1sum\" cannot be used with \"account\""}, "statement substatement as agent \"mbox_sha1sum\" cannot be used with \"mbox\"": {"name": "statement substatement as agent \"mbox_sha1sum\" cannot be used with \"mbox\""}, "statement substatement as agent \"mbox_sha1sum\" cannot be used with \"openid\"": {"name": "statement substatement as agent \"mbox_sha1sum\" cannot be used with \"openid\""}, "statement substatement\"s agent \"mbox_sha1sum\" cannot be used with \"account\"": {"name": "statement substatement\"s agent \"mbox_sha1sum\" cannot be used with \"account\""}, "statement substatement\"s agent \"mbox_sha1sum\" cannot be used with \"mbox\"": {"name": "statement substatement\"s agent \"mbox_sha1sum\" cannot be used with \"mbox\""}, "statement substatement\"s agent \"mbox_sha1sum\" cannot be used with \"openid\"": {"name": "statement substatement\"s agent \"mbox_sha1sum\" cannot be used with \"openid\""}, "statement substatement\"s context instructor \"mbox_sha1sum\" cannot be used with \"account\"": {"name": "statement substatement\"s context instructor \"mbox_sha1sum\" cannot be used with \"account\""}, "statement substatement\"s context instructor \"mbox_sha1sum\" cannot be used with \"mbox\"": {"name": "statement substatement\"s context instructor \"mbox_sha1sum\" cannot be used with \"mbox\""}, "statement substatement\"s context instructor \"mbox_sha1sum\" cannot be used with \"openid\"": {"name": "statement substatement\"s context instructor \"mbox_sha1sum\" cannot be used with \"openid\""}, "An Agent does not use the \"account\" property if \"mbox\", \"mbox_sha1sum\", or \"openid\" are used": {"name": "An Agent does not use the \"account\" property if \"mbox\", \"mbox_sha1sum\", or \"openid\" are used", "1.0.2_ref_text": "Multiplicity, *******.b", "1.0.2_ref": "*******.b", "1.0.3_ref": "Data *******.s2.b2", "1.0.3_link": ["Data.md#*******.s2.b2"]}, "statement actor \"account\" cannot be used with \"mbox\"": {"name": "statement actor \"account\" cannot be used with \"mbox\""}, "statement actor \"account\" cannot be used with \"mbox_sha1sum\"": {"name": "statement actor \"account\" cannot be used with \"mbox_sha1sum\""}, "statement actor \"account\" cannot be used with \"openid": {"name": "statement actor \"account\" cannot be used with \"openid"}, "statement authority \"account\" cannot be used with \"mbox\"": {"name": "statement authority \"account\" cannot be used with \"mbox\""}, "statement authority \"account\" cannot be used with \"mbox_sha1sum\"": {"name": "statement authority \"account\" cannot be used with \"mbox_sha1sum\""}, "statement authority \"account\" cannot be used with \"openid\"": {"name": "statement authority \"account\" cannot be used with \"openid\""}, "statement context instructor \"account\" cannot be used with \"mbox\"": {"name": "statement context instructor \"account\" cannot be used with \"mbox\""}, "statement context instructor \"account\" cannot be used with \"mbox_sha1sum\"": {"name": "statement context instructor \"account\" cannot be used with \"mbox_sha1sum\""}, "statement context instructor \"account\" cannot be used with \"openid\"": {"name": "statement context instructor \"account\" cannot be used with \"openid\""}, "statement substatement as agent \"account\" cannot be used with \"mbox\"": {"name": "statement substatement as agent \"account\" cannot be used with \"mbox\""}, "statement substatement as agent \"account\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement as agent \"account\" cannot be used with \"mbox_sha1sum\""}, "statement substatement as agent \"account\" cannot be used with \"openid\"": {"name": "statement substatement as agent \"account\" cannot be used with \"openid\""}, "statement substatement\"s agent \"account\" cannot be used with \"mbox\"": {"name": "statement substatement\"s agent \"account\" cannot be used with \"mbox\""}, "statement substatement\"s agent \"account\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement\"s agent \"account\" cannot be used with \"mbox_sha1sum\""}, "statement substatement\"s agent \"account\" cannot be used with \"openid\"": {"name": "statement substatement\"s agent \"account\" cannot be used with \"openid\""}, "statement substatement\"s context instructor \"account\" cannot be used with \"mbox\"": {"name": "statement substatement\"s context instructor \"account\" cannot be used with \"mbox\""}, "statement substatement\"s context instructor \"account\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement\"s context instructor \"account\" cannot be used with \"mbox_sha1sum\""}, "statement substatement\"s context instructor \"account\" cannot be used with \"openid\"": {"name": "statement substatement\"s context instructor \"account\" cannot be used with \"openid\""}, "An Agent does not use the \"openid\" property if \"mbox\", \"mbox_sha1sum\", or \"account\" are used": {"name": "An Agent does not use the \"openid\" property if \"mbox\", \"mbox_sha1sum\", or \"account\" are used", "1.0.2_ref_text": "Multiplicity, *******.b", "1.0.2_ref": "*******.b", "1.0.3_ref": "Data *******.s2.b2", "1.0.3_link": ["Data.md#*******.s2.b2"]}, "statement actor \"openid\" cannot be used with \"account\"": {"name": "statement actor \"openid\" cannot be used with \"account\""}, "statement actor \"openid\" cannot be used with \"mbox\"": {"name": "statement actor \"openid\" cannot be used with \"mbox\""}, "statement actor \"openid\" cannot be used with \"mbox_sha1sum\"": {"name": "statement actor \"openid\" cannot be used with \"mbox_sha1sum\""}, "statement authority \"openid\" cannot be used with \"account\"": {"name": "statement authority \"openid\" cannot be used with \"account\""}, "statement authority \"openid\" cannot be used with \"mbox\"": {"name": "statement authority \"openid\" cannot be used with \"mbox\""}, "statement authority \"openid\" cannot be used with \"mbox_sha1sum\"": {"name": "statement authority \"openid\" cannot be used with \"mbox_sha1sum\""}, "statement context instructor \"openid\" cannot be used with \"account\"": {"name": "statement context instructor \"openid\" cannot be used with \"account\""}, "statement context instructor \"openid\" cannot be used with \"mbox\"": {"name": "statement context instructor \"openid\" cannot be used with \"mbox\""}, "statement context instructor \"openid\" cannot be used with \"mbox_sha1sum\"": {"name": "statement context instructor \"openid\" cannot be used with \"mbox_sha1sum\""}, "statement substatement as agent \"openid\" cannot be used with \"account\"": {"name": "statement substatement as agent \"openid\" cannot be used with \"account\""}, "statement substatement as agent \"openid\" cannot be used with \"mbox\"": {"name": "statement substatement as agent \"openid\" cannot be used with \"mbox\""}, "statement substatement as agent \"openid\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement as agent \"openid\" cannot be used with \"mbox_sha1sum\""}, "statement substatement\"s agent \"openid\" cannot be used with \"account\"": {"name": "statement substatement\"s agent \"openid\" cannot be used with \"account\""}, "statement substatement\"s agent \"openid\" cannot be used with \"mbox\"": {"name": "statement substatement\"s agent \"openid\" cannot be used with \"mbox\""}, "statement substatement\"s agent \"openid\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement\"s agent \"openid\" cannot be used with \"mbox_sha1sum\""}, "statement substatement\"s context instructor \"openid\" cannot be used with \"account\"": {"name": "statement substatement\"s context instructor \"openid\" cannot be used with \"account\""}, "statement substatement\"s context instructor \"openid\" cannot be used with \"mbox\"": {"name": "statement substatement\"s context instructor \"openid\" cannot be used with \"mbox\""}, "statement substatement\"s context instructor \"openid\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement\"s context instructor \"openid\" cannot be used with \"mbox_sha1sum\""}, "A Statement's \"attachments\" property is an array of Attachments": {"name": "A Statement's \"attachments\" property is an array of Attachments", "1.0.2_ref_text": "*******.table1.row11.a", "1.0.2_ref": "4.1.table1.row11.a", "1.0.3_ref": "Data 2.4.s1.table1.row11", "1.0.3_link": ["Data.md#2.4.s1.table1.row11"]}, "statement \"attachments\" not an array": {"name": "statement \"attachments\" not an array"}, "An Attachment is an Object": {"name": "An Attachment is an Object", "1.0.2_ref_text": "Definition, 4.1.11", "1.0.2_ref": "4.1.11", "1.0.3_ref": "Data 2.4.11", "1.0.3_link": ["Data.md#2.4.11"]}, "statement \"attachment\" invalid numeric": {"name": "statement \"attachment\" invalid numeric"}, "statement \"attachment\" invalid string": {"name": "statement \"attachment\" invalid string"}, "A \"usageType\" property is an IRI": {"name": "A \"usageType\" property is an IRI", "1.0.2_ref_text": "Multiplicity, 4.1.11.table1.row1.b", "1.0.2_ref": "4.1.11.table1.row1.b", "1.0.3_ref": "Data 2.4.11.s2.table1.row1", "1.0.3_link": ["Data.md#2.4.11.s2.table1.row1"]}, "statement \"usageType\" invalid string": {"name": "statement \"usageType\" invalid string"}, "A \"contentType\" property is an Internet Media/MIME type": {"name": "A \"contentType\" property is an Internet Media/MIME type", "1.0.2_ref_text": "Format, 4.1.11.table1.row4.a, IETF.org", "1.0.2_ref": "4.1.11.table1.row4.a, IETF.org", "1.0.3_ref": "Data 2.4.11.s2.table1.row4", "1.0.3_link": ["Data.md#2.4.11.s2.table1.row4"]}, "statement \"contentType\" invalid string": {"name": "statement \"contentType\" invalid string"}, "A \"length\" property is an Integer": {"name": "A \"length\" property is an Integer", "1.0.2_ref_text": "Format, 4.1.11.table1.row5.a", "1.0.2_ref": "4.1.11.table1.row5.a", "1.0.3_ref": "Data 2.4.11.s2.table1.row5", "1.0.3_link": ["Data.md#2.4.11.s2.table1.row5"]}, "statement \"length\" invalid string": {"name": "statement \"length\" invalid string"}, "A \"sha2\" property is a String": {"name": "A \"sha2\" property is a String", "1.0.2_ref_text": "Format, 4.1.11.table1.row6.a", "1.0.2_ref": "4.1.11.table1.row6.a", "1.0.3_ref": "Data 2.4.11.s2.table1.row6", "1.0.3_link": ["Data.md#2.4.11.s2.table1.row6"]}, "statement \"sha2\" invalid string": {"name": "statement \"sha2\" invalid string"}, "A \"fileUrl\" property is an IRL": {"name": "A \"fileUrl\" property is an IRL", "1.0.2_ref_text": "Format, 4.1.11.table1.row7.a", "1.0.2_ref": "4.1.11.table1.row7.a", "1.0.3_ref": "Data 2.4.11.s2.table1.row7", "1.0.3_link": ["Data.md#2.4.11.s2.table1.row7"]}, "statement \"fileUrl\" invalid string": {"name": "statement \"fileUrl\" invalid string"}, "A \"display\" property is a Language Map": {"name": "A \"display\" property is a Language Map", "1.0.2_ref_text": "Type, 4.1.3.table1.row1.a, 4.1.11.table1.row2.a", "1.0.2_ref": "4.1.3.table1.row2.a, 4.1.11.table1.row2.a", "1.0.3_ref": "Data 2.4.11.s2.table1.row2", "1.0.3_link": ["Data.md#2.4.11.s2.table1.row2"]}, "statement attachment \"description\" language map numeric": {"name": "statement attachment \"description\" language map numeric"}, "statement attachment \"description\" language map string": {"name": "statement attachment \"description\" language map string"}, "An \"authority\" property is an Agent or Group": {"name": "An \"authority\" property is an Agent or Group", "1.0.2_ref_text": "Type, *******.table1.row9.a, *******.table1.row9.b, 4.1.9.a", "1.0.2_ref": "4.1.table1.row9.b, 4.1.9.a", "1.0.3_ref": "Data 2.4.s1.table1.row9", "1.0.3_link": ["Data.md#2.4.s1.table1.row9"]}, "should pass statement authority agent template": {"name": "should pass statement authority agent template"}, "should fail statement authority identified group (mbox)": {"name": "should fail statement authority identified group (mbox)"}, "should fail statement authority identified group": {"name": "should fail statement authority identified group", "1.0.2_ref_text": "mbox_sha1sum"}, "should fail statement authority identified group (openid)": {"name": "should fail statement authority identified group (openid)"}, "should fail statement authority identified group (account)": {"name": "should fail statement authority identified group (account)"}, "An \"authority\" property which is also a Group contains exactly two Agents": {"name": "An \"authority\" property which is also a Group contains exactly two Agents", "1.0.2_ref_text": "Type, *******.table1.row9.a, *******.table1.row9.b, 4.1.9.a", "1.0.2_ref": "4.1.table1.row9.b, 4.1.9.a", "1.0.3_ref": "Data 2.4.9.s3.b1", "1.0.3_link": ["Data.md#2.4.9.s3.b1"]}, "statement \"authority\" invalid one member": {"name": "statement \"authority\" invalid one member"}, "statement \"authority\" invalid three member": {"name": "statement \"authority\" invalid three member"}, "An LRS rejects with error code 400 Bad Request, a Request whose \"authority\" is a Group of more than two Agents": {"name": "An LRS rejects with error code 400 Bad Request, a Request whose \"authority\" is a Group of more than two Agents", "1.0.2_ref_text": "Format, 4.1.9.a", "1.0.2_ref": "4.1.9.a", "1.0.3_ref": "Data 2.4.9.s3.b1", "1.0.3_link": ["Data.md#2.4.9.s3.b1"]}, "Contexts Verify Templates": {"name": "Contexts Verify Templates"}, "should pass statement context template": {"name": "should pass statement context template"}, "should pass substatement context template": {"name": "should pass substatement context template"}, "A \"registration\" property is a UUID": {"name": "A \"registration\" property is a UUID", "1.0.2_ref_text": "Type, 4.1.6.table1.row1.a", "1.0.2_ref": "4.1.6.table1.row1.a", "1.0.3_ref": "Data 2.4.6.s3.table1.row1", "1.0.3_link": ["Data.md#2.4.6.s3.table1.row1"]}, "statement context \"registration\" is object": {"name": "statement context \"registration\" is object"}, "statement context \"registration\" is string": {"name": "statement context \"registration\" is string"}, "statement substatement context \"registration\" is object": {"name": "statement substatement context \"registration\" is object"}, "statement substatement context \"registration\" is string": {"name": "statement substatement context \"registration\" is string"}, "An \"instructor\" property is an Agent": {"name": "An \"instructor\" property is an Agent", "1.0.2_ref_text": "Type, 4.1.6.table1.row2.a", "1.0.2_ref": "4.1.6.table.table1.row2.a", "1.0.3_ref": "Data 2.4.6.s3.table1.row2", "1.0.3_link": ["Data.md#2.4.6.s3.table1.row2"]}, "statement context \"instructor\" is object": {"name": "statement context \"instructor\" is object"}, "statement context \"instructor\" is string": {"name": "statement context \"instructor\" is string"}, "statement substatement context \"instructor\" is object": {"name": "statement substatement context \"instructor\" is object"}, "statement substatement context \"instructor\" is string": {"name": "statement substatement context \"instructor\" is string"}, "An \"team\" property is a Group": {"name": "An \"team\" property is a Group", "1.0.2_ref_text": "Type, 4.1.6.table1.row3.a", "1.0.2_ref": "4.1.6.table1.row3.a", "1.0.3_ref": "Data 2.4.6.s3.table1.row3", "1.0.3_link": ["Data.md#2.4.6.s3.table1.row3"]}, "statement context \"team\" is agent": {"name": "statement context \"team\" is agent"}, "statement context \"team\" is object": {"name": "statement context \"team\" is object"}, "statement context \"team\" is string": {"name": "statement context \"team\" is string"}, "statement substatement context \"team\" is agent": {"name": "statement substatement context \"team\" is agent"}, "statement substatement context \"team\" is object": {"name": "statement substatement context \"team\" is object"}, "statement substatement context \"team\" is string": {"name": "statement substatement context \"team\" is string"}, "A \"contextActivities\" property is an Object": {"name": "A \"contextActivities\" property is an Object", "1.0.2_ref_text": "Type, 4.1.5.table1.row4.a", "1.0.2_ref": "4.1.6.table1.row4.a", "1.0.3_ref": "Data 2.4.6.s3.table1.row4", "1.0.3_link": ["Data.md#2.4.6.s3.table1.row4"]}, "statement context \"contextActivities\" is string": {"name": "statement context \"contextActivities\" is string"}, "statement substatement context \"contextActivities\" is string": {"name": "statement substatement context \"contextActivities\" is string"}, "A \"contextActivities\" property contains one or more key/value pairs": {"name": "A \"contextActivities\" property contains one or more key/value pairs", "1.0.2_ref_text": "Format, 4.1.a, 4.1.6.2.b", "1.0.2_ref": "4.1.a, 4.1.6.2.b", "1.0.3_ref": "Data 2.4.6.2.s4.b2", "1.0.3_link": ["Data.md#2.4.6.2.s4.b2"]}, "statement context \"contextActivities\" is empty": {"name": "statement context \"contextActivities\" is empty"}, "statement substatement context \"contextActivities\" is empty": {"name": "statement substatement context \"contextActivities\" is empty"}, "A \"contextActivities\" property's \"key\" has a value of \"parent\", \"grouping\", \"category\", or \"other\"": {"name": "A \"contextActivities\" property's \"key\" has a value of \"parent\", \"grouping\", \"category\", or \"other\"", "1.0.2_ref_text": "Format, 4.1.6.2.a", "1.0.2_ref": "4.1.6.2.a", "1.0.3_ref": "Data 2.4.6.2.s4.b1", "1.0.3_link": ["Data.md#2.4.6.2.s4.b1"]}, "statement context \"contextActivities\" is \"parent\"": {"name": "statement context \"contextActivities\" is \"parent\""}, "statement context \"contextActivities\" is \"grouping\"": {"name": "statement context \"contextActivities\" is \"grouping\""}, "statement context \"contextActivities\" is \"category\"": {"name": "statement context \"contextActivities\" is \"category\""}, "statement context \"contextActivities\" is \"other\"": {"name": "statement context \"contextActivities\" is \"other\""}, "statement substatement context \"contextActivities\" is \"parent\"": {"name": "statement substatement context \"contextActivities\" is \"parent\""}, "statement substatement context \"contextActivities\" is \"grouping\"": {"name": "statement substatement context \"contextActivities\" is \"grouping\""}, "statement substatement context \"contextActivities\" is \"category\"": {"name": "statement substatement context \"contextActivities\" is \"category\""}, "statement substatement context \"contextActivities\" is \"other\"": {"name": "statement substatement context \"contextActivities\" is \"other\""}, "A \"contextActivities\" property's \"value\" is an Activity": {"name": "A \"contextActivities\" property's \"value\" is an Activity", "1.0.2_ref_text": "Format, 4.1.6.2.a", "1.0.2_ref": "4.1.6.2.a", "1.0.3_ref": "Data 2.4.6.2.s4.b2", "1.0.3_link": ["Data.md#2.4.6.2.s4.b2"]}, "statement context \"contextActivities parent\" value is activity array": {"name": "statement context \"contextActivities parent\" value is activity array"}, "statement context \"contextActivities grouping\" value is activity array": {"name": "statement context \"contextActivities grouping\" value is activity array"}, "statement context \"contextActivities category\" value is activity array": {"name": "statement context \"contextActivities category\" value is activity array"}, "statement context \"contextActivities other\" value is activity array": {"name": "statement context \"contextActivities other\" value is activity array"}, "statement substatement context \"contextActivities parent\" value is activity array": {"name": "statement substatement context \"contextActivities parent\" value is activity array"}, "statement substatement context \"contextActivities grouping\" value is activity array": {"name": "statement substatement context \"contextActivities grouping\" value is activity array"}, "statement substatement context \"contextActivities category\" value is activity array": {"name": "statement substatement context \"contextActivities category\" value is activity array"}, "statement substatement context \"contextActivities other\" value is activity array": {"name": "statement substatement context \"contextActivities other\" value is activity array"}, "A ContextActivity is defined as a single Activity of the \"value\" of the \"contextActivities\" property": {"name": "A ContextActivity is defined as a single Activity of the \"value\" of the \"contextActivities\" property (definition)", "1.0.2_ref": "definition", "1.0.3_ref": "Data 2.4.6.2.s4.b2", "1.0.3_link": ["Data.md#2.4.6.2.s4.b2"]}, "statement context \"contextActivities parent\" value is activity": {"name": "statement context \"contextActivities parent\" value is activity"}, "statement context \"contextActivities grouping\" value is activity": {"name": "statement context \"contextActivities grouping\" value is activity"}, "statement context \"contextActivities category\" value is activity": {"name": "statement context \"contextActivities category\" value is activity"}, "statement context \"contextActivities other\" value is activity": {"name": "statement context \"contextActivities other\" value is activity"}, "statement substatement context \"contextActivities parent\" value is activity": {"name": "statement substatement context \"contextActivities parent\" value is activity"}, "statement substatement context \"contextActivities grouping\" value is activity": {"name": "statement substatement context \"contextActivities grouping\" value is activity"}, "statement substatement context \"contextActivities category\" value is activity": {"name": "statement substatement context \"contextActivities category\" value is activity"}, "statement substatement context \"contextActivities other\" value is activity": {"name": "statement substatement context \"contextActivities other\" value is activity"}, "A \"revision\" property is a String": {"name": "A \"revision\" property is a String", "1.0.2_ref_text": "Type, 4.1.6.table1.row5.a", "1.0.2_ref": "4.1.6.table1.row5.a", "1.0.3_ref": "Data 2.4.6.s3.table1.row5", "1.0.3_link": ["Data.md#2.4.6.s3.table1.row5"]}, "statement context \"revision\" is numeric": {"name": "statement context \"revision\" is numeric"}, "statement context \"revision\" is object": {"name": "statement context \"revision\" is object"}, "statement substatement context \"revision\" is numeric": {"name": "statement substatement context \"revision\" is numeric"}, "statement substatement context \"revision\" is object": {"name": "statement substatement context \"revision\" is object"}, "A Statement cannot contain both a \"revision\" property in its \"context\" property and have the value of the \"object\" property's \"objectType\" be anything but \"Activity\"": {"name": "A Statement cannot contain both a \"revision\" property in its \"context\" property and have the value of the \"object\" property's \"objectType\" be anything but \"Activity\"", "1.0.2_ref_text": "4.1.6.a", "1.0.2_ref": "4.1.6.a", "1.0.3_ref": "Data 2.4.6.s4.b1", "1.0.3_link": ["Data.md#2.4.6.s4.b1"]}, "statement context \"revision\" is invalid with object agent": {"name": "statement context \"revision\" is invalid with object agent"}, "statement context \"revision\" is invalid with object group": {"name": "statement context \"revision\" is invalid with object group"}, "statement context \"revision\" is invalid with statementref": {"name": "statement context \"revision\" is invalid with statementref"}, "statement context \"revision\" is invalid with substatement": {"name": "statement context \"revision\" is invalid with substatement"}, "statement substatement context \"revision\" is invalid with object agent": {"name": "statement substatement context \"revision\" is invalid with object agent"}, "statement substatement context \"revision\" is invalid with object group": {"name": "statement substatement context \"revision\" is invalid with object group"}, "statement substatement context \"revision\" is invalid with statementref": {"name": "statement substatement context \"revision\" is invalid with statementref"}, "A \"platform\" property is a String": {"name": "A \"platform\" property is a String", "1.0.2_ref_text": "Type, 4.1.6.table1.row6.a", "1.0.2_ref": "4.1.6.table1.row6.a", "1.0.3_ref": "Data 2.4.6.s3.table1.row6", "1.0.3_link": ["Data.md#2.4.6.s3.table1.row6"]}, "statement context \"platform\" is numeric": {"name": "statement context \"platform\" is numeric"}, "statement context \"platform\" is object": {"name": "statement context \"platform\" is object"}, "statement substatement context \"platform\" is numeric": {"name": "statement substatement context \"platform\" is numeric"}, "statement substatement context \"platform\" is object": {"name": "statement substatement context \"platform\" is object"}, "A Statement cannot contain both a \"platform\" property in its \"context\" property and have the value of the \"object\" property's \"objectType\" be anything but \"Activity\"": {"name": "A Statement cannot contain both a \"platform\" property in its \"context\" property and have the value of the \"object\" property's \"objectType\" be anything but \"Activity\"", "1.0.2_ref_text": "4.1.6.b", "1.0.2_ref": "4.1.6.b", "1.0.3_ref": "Data 2.4.6.s4.b2", "1.0.3_link": ["Data.md#2.4.6.s4.b2"]}, "statement context \"platform\" is invalid with object agent": {"name": "statement context \"platform\" is invalid with object agent"}, "statement context \"platform\" is invalid with object group": {"name": "statement context \"platform\" is invalid with object group"}, "statement context \"platform\" is invalid with statementref": {"name": "statement context \"platform\" is invalid with statementref"}, "statement context \"platform\" is invalid with substatement": {"name": "statement context \"platform\" is invalid with substatement"}, "statement substatement context \"platform\" is invalid with object agent": {"name": "statement substatement context \"platform\" is invalid with object agent"}, "statement substatement context \"platform\" is invalid with object group": {"name": "statement substatement context \"platform\" is invalid with object group"}, "statement substatement context \"platform\" is invalid with statementref": {"name": "statement substatement context \"platform\" is invalid with statementref"}, "A \"language\" property is a String": {"name": "A \"language\" property is a String", "1.0.2_ref_text": "Type, 4.1.6.table1.row7.a", "1.0.2_ref": "4.1.6.table1.row7.a", "1.0.3_ref": "Data 2.4.6.s3.table1.row7", "1.0.3_link": ["Data.md#2.4.6.s3.table1.row7"]}, "statement context \"language\" is numeric": {"name": "statement context \"language\" is numeric"}, "statement context \"language\" is object": {"name": "statement context \"language\" is object"}, "statement substatement context \"language\" is numeric": {"name": "statement substatement context \"language\" is numeric"}, "statement substatement context \"language\" is object": {"name": "statement substatement context \"language\" is object"}, "A \"language\" property follows RFC5646": {"name": "A \"language\" property follows RFC5646", "1.0.2_ref_text": "Format, 4.1.6.table1.row7.a, RFC5646", "1.0.2_ref": "4.1.6.table1.row7.a, RFC5646", "1.0.3_ref": "Data 2.4.6.s3.table1.row7, RFC5646", "1.0.3_link": ["Data.md#2.4.6.s3.table1.row7"]}, "statement context \"language\" is is invalid language": {"name": "statement context \"language\" is is invalid language"}, "statement substatement context \"language\" is is invalid language": {"name": "statement substatement context \"language\" is is invalid language"}, "A \"statement\" property is a Statement Reference": {"name": "A \"statement\" property is a Statement Reference", "1.0.2_ref_text": "Type, 4.1.6.table1.row8.a", "1.0.2_ref": "4.1.6.table1.row8.a", "1.0.3_ref": "Data 2.4.6.s3.table1.row8", "1.0.3_link": ["Data.md#2.4.6.s3.table1.row8"]}, "statement context \"statement\" invalid with \"statementref\"": {"name": "statement context \"statement\" invalid with \"statementref\""}, "statement context \"statement\" invalid with \"id\" not UUID": {"name": "statement context \"statement\" invalid with \"id\" not UUID"}, "statement substatement context \"statement\" invalid with \"statementref\"": {"name": "statement substatement context \"statement\" invalid with \"statementref\""}, "statement substatement context \"statement\" invalid with \"id\" not UUID": {"name": "statement substatement context \"statement\" invalid with \"id\" not UUID"}, "An Extension is defined as an Object of any \"extensions\" property": {"name": "An Extension is defined as an Object of any \"extensions\" property", "1.0.2_ref_text": "Multiplicity, 5.3", "1.0.2_ref": "5.3", "1.0.3_ref": "Data 4.1.s2", "1.0.3_link": ["Data.md#4.1.s2"]}, "statement activity extensions valid boolean": {"name": "statement activity extensions valid boolean"}, "statement activity extensions valid numeric": {"name": "statement activity extensions valid numeric"}, "statement activity extensions valid object": {"name": "statement activity extensions valid object"}, "statement activity extensions valid string": {"name": "statement activity extensions valid string"}, "statement result extensions valid boolean": {"name": "statement result extensions valid boolean"}, "statement result extensions valid numeric": {"name": "statement result extensions valid numeric"}, "statement result extensions valid object": {"name": "statement result extensions valid object"}, "statement result extensions valid string": {"name": "statement result extensions valid string"}, "statement context extensions valid boolean": {"name": "statement context extensions valid boolean"}, "statement context extensions valid numeric": {"name": "statement context extensions valid numeric"}, "statement context extensions valid object": {"name": "statement context extensions valid object"}, "statement context extensions valid string": {"name": "statement context extensions valid string"}, "statement substatement activity extensions valid boolean": {"name": "statement substatement activity extensions valid boolean"}, "statement substatement activity extensions valid numeric": {"name": "statement substatement activity extensions valid numeric"}, "statement substatement activity extensions valid object": {"name": "statement substatement activity extensions valid object"}, "statement substatement activity extensions valid string": {"name": "statement substatement activity extensions valid string"}, "statement substatement result extensions valid boolean": {"name": "statement substatement result extensions valid boolean"}, "statement substatement result extensions valid numeric": {"name": "statement substatement result extensions valid numeric"}, "statement substatement result extensions valid object": {"name": "statement substatement result extensions valid object"}, "statement substatement result extensions valid string": {"name": "statement substatement result extensions valid string"}, "statement substatement context extensions valid boolean": {"name": "statement substatement context extensions valid boolean"}, "statement substatement context extensions valid numeric": {"name": "statement substatement context extensions valid numeric"}, "statement substatement context extensions valid object": {"name": "statement substatement context extensions valid object"}, "statement substatement context extensions valid string": {"name": "statement substatement context extensions valid string"}, "An Extension can be null, an empty string, objects with nothing in them when using POST.": {"name": "An Extension can be null, an empty string, objects with nothing in them when using POST.", "1.0.2_ref_text": "Format, 5.3", "1.0.2_ref": "5.3", "1.0.3_ref": "Data 4.1, XAPI-00119", "1.0.3_link": ["Data.md#4.1"]}, "An Extension can be null, an empty string, objects with nothing in them when using PUT.": {"name": "An Extension can be null, an empty string, objects with nothing in them when using PUT.", "1.0.2_ref_text": "Format, 5.3", "1.0.2_ref": "5.3", "1.0.3_ref": "Data 4.1, XAPI-00119", "1.0.3_link": ["Data.md#4.1"]}, "statement activity extensions can be empty": {"name": "statement activity extensions can be empty"}, "statement result extensions can be empty": {"name": "statement result extensions can be empty"}, "statement context extensions can be empty": {"name": "statement context extensions can be empty"}, "statement substatement activity extensions can be empty": {"name": "statement substatement activity extensions can be empty"}, "statement substatement result extensions can be empty": {"name": "statement substatement result extensions can be empty"}, "statement substatement context extensions can be empty": {"name": "statement substatement context extensions can be empty"}, "An Extension \"key\" is an IRI": {"name": "An Extension \"key\" is an IRI", "1.0.2_ref_text": "Format, 5.3.a", "1.0.2_ref": "5.3.a", "1.0.3_ref": "Data 4.1.s3.b1", "1.0.3_link": ["Data.md#4.1.s3.b1"]}, "statement activity extensions key is not an IRI": {"name": "statement activity extensions key is not an IRI"}, "statement result extensions key is not an IRI": {"name": "statement result extensions key is not an IRI"}, "statement context extensions key is not an IRI": {"name": "statement context extensions key is not an IRI"}, "statement substatement activity extensions key is not an IRI": {"name": "statement substatement activity extensions key is not an IRI"}, "statement substatement result extensions key is not an IRI": {"name": "statement substatement result extensions key is not an IRI"}, "statement substatement context extensions key is not an IRI": {"name": "statement substatement context extensions key is not an IRI"}, "Groups Verify Templates": {"name": "Groups Verify Templates"}, "should pass statement context team template": {"name": "should pass statement context team template"}, "should pass statement substatement as group template": {"name": "should pass statement substatement as group template"}, "should pass statement substatement\"s group template": {"name": "should pass statement substatement\"s group template"}, "should pass statement substatement\"s context team template": {"name": "should pass statement substatement\"s context team template"}, "A Group is defined by \"objectType\" of an \"actor\" property or \"object\" property with value \"Group\"": {"name": "A Group is defined by \"objectType\" of an \"actor\" property or \"object\" property with value \"Group\"", "1.0.2_ref_text": "*******.table1.row2, *******.a", "1.0.2_ref": "*******.table1.row2, *******.a", "1.0.3_ref": "Data *******.s2.table2.row1", "1.0.3_link": ["Data.md#*******.s2.table2.row1"]}, "statement actor \"objectType\" accepts \"Group\"": {"name": "statement actor \"objectType\" accepts \"Group\""}, "statement authority \"objectType\" accepts \"Group\"": {"name": "statement authority \"objectType\" accepts \"Group\""}, "statement context instructor \"objectType\" accepts \"Group\"": {"name": "statement context instructor \"objectType\" accepts \"Group\""}, "statement context team \"objectType\" accepts \"Group\"": {"name": "statement context team \"objectType\" accepts \"Group\""}, "statement substatement as group \"objectType\" accepts \"Group\"": {"name": "statement substatement as group \"objectType\" accepts \"Group\""}, "statement substatement\"s group \"objectType\" accepts \"Group\"": {"name": "statement substatement\"s group \"objectType\" accepts \"Group\""}, "statement substatement\"s context instructor \"objectType\" accepts \"Group\"": {"name": "statement substatement\"s context instructor \"objectType\" accepts \"Group\""}, "statement substatement\"s context team \"objectType\" accepts \"Group\"": {"name": "statement substatement\"s context team \"objectType\" accepts \"Group\""}, "An Anonymous Group is defined by \"objectType\" of an \"actor\" or \"object\" with value \"Group\" and by none of \"mbox\", \"mbox_sha1sum\", \"openid\", or \"account\" being used": {"name": "An Anonymous Group is defined by \"objectType\" of an \"actor\" or \"object\" with value \"Group\" and by none of \"mbox\", \"mbox_sha1sum\", \"openid\", or \"account\" being used", "1.0.2_ref_text": "*******.table1.row2, *******.table1", "1.0.2_ref": "*******.table1", "1.0.3_ref": "Data *******.s2.table1.row1", "1.0.3_link": ["Data.md#*******.s2.table1.row1"]}, "statement actor does not require functional identifier": {"name": "statement actor does not require functional identifier"}, "statement authority does not require functional identifier": {"name": "statement authority does not require functional identifier"}, "statement context instructor does not require functional identifier": {"name": "statement context instructor does not require functional identifier"}, "statement context team does not require functional identifier": {"name": "statement context team does not require functional identifier"}, "statement substatement as group does not require functional identifier": {"name": "statement substatement as group does not require functional identifier"}, "statement substatement\"s group does not require functional identifier": {"name": "statement substatement\"s group does not require functional identifier"}, "statement substatement\"s context instructor does not require functional identifier": {"name": "statement substatement\"s context instructor does not require functional identifier"}, "statement substatement\"s context team does not require functional identifier": {"name": "statement substatement\"s context team does not require functional identifier"}, "An Anonymous Group uses the \"member\" property": {"name": "An Anonymous Group uses the \"member\" property", "1.0.2_ref_text": "Multiplicity, *******.table1.row3.b", "1.0.2_ref": "*******.table1.row3.c", "1.0.3_ref": "Data *******.s2.table1.row3", "1.0.3_link": ["Data.md#*******.s2.table1.row3"]}, "statement actor anonymous group missing member": {"name": "statement actor anonymous group missing member"}, "statement authority anonymous group missing member": {"name": "statement authority anonymous group missing member"}, "statement context instructor anonymous group missing member": {"name": "statement context instructor anonymous group missing member"}, "statement context team anonymous group missing member": {"name": "statement context team anonymous group missing member"}, "statement substatement as group anonymous group missing member": {"name": "statement substatement as group anonymous group missing member"}, "statement substatement\"s group anonymous group missing member": {"name": "statement substatement\"s group anonymous group missing member"}, "statement substatement\"s context instructor anonymous group missing member": {"name": "statement substatement\"s context instructor anonymous group missing member"}, "statement substatement\"s context team anonymous group missing member": {"name": "statement substatement\"s context team anonymous group missing member"}, "The \"member\" property is an array of Objects following Agent requirements": {"name": "The \"member\" property is an array of Objects following Agent requirements", "1.0.2_ref_text": "*******.table1.row3.a", "1.0.2_ref": "*******.table1.row3.a", "1.0.3_ref": "Data *******.s2.table2.row3", "1.0.3_link": ["Data.md#*******.s2.table2.row3"]}, "statement actor requires member type \"array\"": {"name": "statement actor requires member type \"array\""}, "statement authority requires member type \"array\"": {"name": "statement authority requires member type \"array\""}, "statement context instructor requires member type \"array\"": {"name": "statement context instructor requires member type \"array\""}, "statement context team requires member type \"array\"": {"name": "statement context team requires member type \"array\""}, "statement substatement as group requires member type \"array\"": {"name": "statement substatement as group requires member type \"array\""}, "statement substatement\"s group requires member type \"array\"": {"name": "statement substatement\"s group requires member type \"array\""}, "statement substatement\"s context instructor requires member type \"array\"": {"name": "statement substatement\"s context instructor requires member type \"array\""}, "statement substatement\"s context team requires member type \"array\"": {"name": "statement substatement\"s context team requires member type \"array\""}, "Statement authority shall only be an anonymous group with two members": {"name": "Statement authority shall only be an anonymous group with two members", "1.0.2_ref": "4.1.9.a, *******", "1.0.3_ref": "Data 2.4.9.s3.b1", "1.0.3_link": ["Data.md#2.4.9.s3.b1"]}, "statement authority identified group is rejected": {"name": "statement authority identified group is rejected"}, "statement authority anonymous group with two members is accepted": {"name": "statement authority anonymous group with two members is accepted"}, "statement authority anonymous group without two members is rejected": {"name": "statement authority anonymous group without two members is rejected"}, "An Identified Group is defined by \"objectType\" of an \"actor\" or \"object\" with value \"Group\" and by one of \"mbox\", \"mbox_sha1sum\", \"openid\", or \"account\" being used": {"name": "An Identified Group is defined by \"objectType\" of an \"actor\" or \"object\" with value \"Group\" and by one of \"mbox\", \"mbox_sha1sum\", \"openid\", or \"account\" being used", "1.0.2_ref_text": "*******.table1.row2, *******.table2", "1.0.2_ref": "*******.table1.row2, *******.table2", "1.0.3_ref": "Data *******.s2.table2.row1", "1.0.3_link": ["Data.md#*******.s2.table2.row1"]}, "statement actor identified group accepts \"mbox\"": {"name": "statement actor identified group accepts \"mbox\""}, "statement actor identified group accepts \"mbox_sha1sum\"": {"name": "statement actor identified group accepts \"mbox_sha1sum\""}, "statement actor identified group accepts \"openid\"": {"name": "statement actor identified group accepts \"openid\""}, "statement actor identified group accepts \"account\"": {"name": "statement actor identified group accepts \"account\""}, "statement context instructor identified group accepts \"mbox\"": {"name": "statement context instructor identified group accepts \"mbox\""}, "statement context instructor identified group accepts \"mbox_sha1sum\"": {"name": "statement context instructor identified group accepts \"mbox_sha1sum\""}, "statement context instructor identified group accepts \"openid\"": {"name": "statement context instructor identified group accepts \"openid\""}, "statement context instructor identified group accepts \"account\"": {"name": "statement context instructor identified group accepts \"account\""}, "statement context team identified group accepts \"mbox\"": {"name": "statement context team identified group accepts \"mbox\""}, "statement context team identified group accepts \"mbox_sha1sum\"": {"name": "statement context team identified group accepts \"mbox_sha1sum\""}, "statement context team identified group accepts \"openid\"": {"name": "statement context team identified group accepts \"openid\""}, "statement context team identified group accepts \"account\"": {"name": "statement context team identified group accepts \"account\""}, "statement substatement as group identified group accepts \"mbox\"": {"name": "statement substatement as group identified group accepts \"mbox\""}, "statement substatement as group identified group accepts \"mbox_sha1sum\"": {"name": "statement substatement as group identified group accepts \"mbox_sha1sum\""}, "statement substatement as group identified group accepts \"openid\"": {"name": "statement substatement as group identified group accepts \"openid\""}, "statement substatement as group identified group accepts \"account\"": {"name": "statement substatement as group identified group accepts \"account\""}, "statement substatement\"s group identified group accepts \"mbox\"'": {"name": "statement substatement\"s group identified group accepts \"mbox\"'"}, "statement substatement\"s group identified group accepts \"mbox_sha1sum\"": {"name": "statement substatement\"s group identified group accepts \"mbox_sha1sum\""}, "statement substatement\"s group identified group accepts \"openid\"": {"name": "statement substatement\"s group identified group accepts \"openid\""}, "statement substatement\"s group identified group accepts \"account\"": {"name": "statement substatement\"s group identified group accepts \"account\""}, "statement substatement\"s context instructor identified group accepts \"mbox\"": {"name": "statement substatement\"s context instructor identified group accepts \"mbox\""}, "statement substatement\"s context instructor identified group accepts \"mbox_sha1sum\"": {"name": "statement substatement\"s context instructor identified group accepts \"mbox_sha1sum\""}, "statement substatement\"s context instructor identified group accepts \"openid\"": {"name": "statement substatement\"s context instructor identified group accepts \"openid\""}, "statement substatement\"s context instructor identified group accepts \"account\"": {"name": "statement substatement\"s context instructor identified group accepts \"account\""}, "statement substatement\"s context team identified group accepts \"mbox\"": {"name": "statement substatement\"s context team identified group accepts \"mbox\""}, "statement substatement\"s context team identified group accepts \"mbox_sha1sum\"": {"name": "statement substatement\"s context team identified group accepts \"mbox_sha1sum\""}, "statement substatement\"s context team identified group accepts \"openid\"": {"name": "statement substatement\"s context team identified group accepts \"openid\""}, "statement substatement\"s context team identified group accepts \"account\"": {"name": "statement substatement\"s context team identified group accepts \"account\""}, "An Identified Group uses one of the following properties: \"mbox\", \"mbox_sha1sum\", \"openid\", \"account\"": {"name": "An Identified Group uses one of the following properties: \"mbox\", \"mbox_sha1sum\", \"openid\", \"account\"", "1.0.2_ref_text": "Multiplicity, *******.a", "1.0.2_ref": "*******.table2.row4", "1.0.3_ref": "Data *******.s2.table2.row4", "1.0.3_link": ["Data.md#*******.s2.table2.row4"]}, "statement substatement\"s group identified group accepts \"mbox\"": {"name": "statement substatement\"s group identified group accepts \"mbox\""}, "An Identified Group does not use the \"mbox\" property if \"mbox_sha1sum\", \"openid\", or \"account\" are used": {"name": "An Identified Group does not use the \"mbox\" property if \"mbox_sha1sum\", \"openid\", or \"account\" are used", "1.0.2_ref_text": "Multiplicity, *******.b", "1.0.2_ref": "*******.f", "1.0.3_ref": "Data *******.s5.b1", "1.0.3_link": ["Data.md#*******.s5.b1"]}, "statement context team \"mbox\" cannot be used with \"account\"": {"name": "statement context team \"mbox\" cannot be used with \"account\""}, "statement context team \"mbox\" cannot be used with \"mbox_sha1sum\"": {"name": "statement context team \"mbox\" cannot be used with \"mbox_sha1sum\""}, "statement context team \"mbox\" cannot be used with \"openid\"": {"name": "statement context team \"mbox\" cannot be used with \"openid\""}, "statement substatement as group \"mbox\" cannot be used with \"account\"": {"name": "statement substatement as group \"mbox\" cannot be used with \"account\""}, "statement substatement as group \"mbox\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement as group \"mbox\" cannot be used with \"mbox_sha1sum\""}, "statement substatement as group \"mbox\" cannot be used with \"openid\"": {"name": "statement substatement as group \"mbox\" cannot be used with \"openid\""}, "statement substatement\"s context team \"mbox\" cannot be used with \"account\"": {"name": "statement substatement\"s context team \"mbox\" cannot be used with \"account\""}, "statement substatement\"s context team \"mbox\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement\"s context team \"mbox\" cannot be used with \"mbox_sha1sum\""}, "statement substatement\"s context team \"mbox\" cannot be used with \"openid\"": {"name": "statement substatement\"s context team \"mbox\" cannot be used with \"openid\""}, "An Identified Group does not use the \"mbox_sha1sum\" property if \"mbox\", \"openid\", or \"account\" are used": {"name": "An Identified Group does not use the \"mbox_sha1sum\" property if \"mbox\", \"openid\", or \"account\" are used", "1.0.2_ref_text": "Multiplicity, *******.b", "1.0.2_ref": "*******.f", "1.0.3_ref": "Data *******.s5.b1", "1.0.3_link": ["Data.md#*******.s5.b1"]}, "statement context team \"mbox_sha1sum\" cannot be used with \"openid\"": {"name": "statement context team \"mbox_sha1sum\" cannot be used with \"openid\""}, "statement context team \"mbox_sha1sum\" cannot be used with \"mbox\"": {"name": "statement context team \"mbox_sha1sum\" cannot be used with \"mbox\""}, "statement substatement as group \"mbox_sha1sum\" cannot be used with \"account\"": {"name": "statement substatement as group \"mbox_sha1sum\" cannot be used with \"account\""}, "statement substatement as group \"mbox_sha1sum\" cannot be used with \"mbox\"": {"name": "statement substatement as group \"mbox_sha1sum\" cannot be used with \"mbox\""}, "statement substatement as group \"mbox_sha1sum\" cannot be used with \"openid\"": {"name": "statement substatement as group \"mbox_sha1sum\" cannot be used with \"openid\""}, "statement substatement\"s context team \"mbox_sha1sum\" cannot be used with \"account\"": {"name": "statement substatement\"s context team \"mbox_sha1sum\" cannot be used with \"account\""}, "statement substatement\"s context team \"mbox_sha1sum\" cannot be used with \"mbox\"": {"name": "statement substatement\"s context team \"mbox_sha1sum\" cannot be used with \"mbox\""}, "statement substatement\"s context team \"mbox_sha1sum\" cannot be used with \"openid\"": {"name": "statement substatement\"s context team \"mbox_sha1sum\" cannot be used with \"openid\""}, "An Identified Group does not use the \"openid\" property if \"mbox\", \"mbox_sha1sum\", or \"account\" are used": {"name": "An Identified Group does not use the \"openid\" property if \"mbox\", \"mbox_sha1sum\", or \"account\" are used", "1.0.2_ref_text": "Multiplicity, *******.b", "1.0.2_ref": "*******.f", "1.0.3_ref": "Data *******.s5.b1", "1.0.3_link": ["Data.md#*******.s5.b1"]}, "statement actor \"openid\" cannot be used with \"account": {"name": "statement actor \"openid\" cannot be used with \"account"}, "statement context team \"openid\" cannot be used with \"account\"": {"name": "statement context team \"openid\" cannot be used with \"account\""}, "statement context team \"openid\" cannot be used with \"mbox\"": {"name": "statement context team \"openid\" cannot be used with \"mbox\""}, "statement context team \"openid\" cannot be used with \"mbox_sha1sum\"": {"name": "statement context team \"openid\" cannot be used with \"mbox_sha1sum\""}, "statement substatement as group \"openid\" cannot be used with \"account\"": {"name": "statement substatement as group \"openid\" cannot be used with \"account\""}, "statement substatement as group \"openid\" cannot be used with \"mbox\"": {"name": "statement substatement as group \"openid\" cannot be used with \"mbox\""}, "statement substatement as group \"openid\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement as group \"openid\" cannot be used with \"mbox_sha1sum\""}, "statement substatement\"s context team \"openid\" cannot be used with \"account\"": {"name": "statement substatement\"s context team \"openid\" cannot be used with \"account\""}, "statement substatement\"s context team \"openid\" cannot be used with \"mbox\"": {"name": "statement substatement\"s context team \"openid\" cannot be used with \"mbox\""}, "statement substatement\"s context team \"openid\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement\"s context team \"openid\" cannot be used with \"mbox_sha1sum\""}, "An Identified Group does not use the \"account\" property if \"mbox\", \"mbox_sha1sum\", or \"openid\" are used": {"name": "An Identified Group does not use the \"account\" property if \"mbox\", \"mbox_sha1sum\", or \"openid\" are used", "1.0.2_ref_text": "Multiplicity, *******.b", "1.0.2_ref": "*******.f", "1.0.3_ref": "Data *******.s5.b1", "1.0.3_link": ["Data.md#*******.s5.b1"]}, "statement actor \"account\" cannot be used with \"openid\"": {"name": "statement actor \"account\" cannot be used with \"openid\""}, "statement authority \"account\" cannot be used with \"openid\"'": {"name": "statement authority \"account\" cannot be used with \"openid\"'"}, "statement context team \"account\" cannot be used with \"mbox\"": {"name": "statement context team \"account\" cannot be used with \"mbox\""}, "statement context team \"account\" cannot be used with \"mbox_sha1sum\"": {"name": "statement context team \"account\" cannot be used with \"mbox_sha1sum\""}, "statement context team \"account\" cannot be used with \"openid\"": {"name": "statement context team \"account\" cannot be used with \"openid\""}, "statement substatement as group \"account\" cannot be used with \"mbox\"": {"name": "statement substatement as group \"account\" cannot be used with \"mbox\""}, "statement substatement as group \"account\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement as group \"account\" cannot be used with \"mbox_sha1sum\""}, "statement substatement as group \"account\" cannot be used with \"openid\"": {"name": "statement substatement as group \"account\" cannot be used with \"openid\""}, "statement substatement\"s context team \"account\" cannot be used with \"mbox\"": {"name": "statement substatement\"s context team \"account\" cannot be used with \"mbox\""}, "statement substatement\"s context team \"account\" cannot be used with \"mbox_sha1sum\"": {"name": "statement substatement\"s context team \"account\" cannot be used with \"mbox_sha1sum\""}, "statement substatement\"s context team \"account\" cannot be used with \"openid\"": {"name": "statement substatement\"s context team \"account\" cannot be used with \"openid\""}, "Languages Verify Templates": {"name": "Languages Verify Templates"}, "should pass statement verb template": {"name": "should pass statement verb template"}, "should pass statement object template": {"name": "should pass statement object template"}, "should pass statement attachment template": {"name": "should pass statement attachment template"}, "should pass statement substatement verb template": {"name": "should pass statement substatement verb template"}, "should pass statement substatement object template": {"name": "should pass statement substatement object template"}, "A Language Map is defined as an array of language tag/String pairs has at least 1 entry": {"name": "A Language Map is defined as an array of language tag/String pairs has at least 1 entry"}, "statement verb \"display\" language map needs one entry": {"name": "statement verb \"display\" language map needs one entry"}, "statement verb \"display\" language map must be object": {"name": "statement verb \"display\" language map must be object"}, "statement object \"name\" language map needs one entry": {"name": "statement object \"name\" language map needs one entry"}, "statement object \"name\" language map must be object": {"name": "statement object \"name\" language map must be object"}, "statement object \"description\" language map needs one entry": {"name": "statement object \"description\" language map needs one entry"}, "statement object \"description\" language map must be object": {"name": "statement object \"description\" language map must be object"}, "statement attachment \"display\" language map needs one entry": {"name": "statement attachment \"display\" language map needs one entry"}, "statement attachment \"display\" language map must be object": {"name": "statement attachment \"display\" language map must be object"}, "statement attachment \"description\" language map needs one entry": {"name": "statement attachment \"description\" language map needs one entry"}, "statement attachment \"description\" language map must be object": {"name": "statement attachment \"description\" language map must be object"}, "statement substatement verb \"display\" language map needs one entry": {"name": "statement substatement verb \"display\" language map needs one entry"}, "statement substatement verb \"display\" language map must be object": {"name": "statement substatement verb \"display\" language map must be object"}, "statement substatement activity \"name\" language map needs one entry": {"name": "statement substatement activity \"name\" language map needs one entry"}, "statement substatement activity \"name\" language map must be object": {"name": "statement substatement activity \"name\" language map must be object"}, "statement substatement activity \"description\" language map needs one entry": {"name": "statement substatement activity \"description\" language map needs one entry"}, "statement substatement activity \"description\" language map must be object": {"name": "statement substatement activity \"description\" language map must be object"}, "A Language Map follows RFC5646": {"name": "A Language Map follows RFC5646", "1.0.2_ref_text": "Format, 5.2.a, RFC5646", "1.0.2_ref": "5.2.a, RFC5646", "1.0.3_ref": "Data 4.2.s1, RFC5646", "1.0.3_link": ["Data.md#4.2.s1"]}, "statement verb \"display\" language map invalid": {"name": "statement verb \"display\" language map invalid"}, "statement object \"name\" language map invalid": {"name": "statement object \"name\" language map invalid"}, "statement object \"description\" language map invalid": {"name": "statement object \"description\" language map invalid"}, "statement attachment \"display\" language map invalid": {"name": "statement attachment \"display\" language map invalid"}, "statement attachment \"description\" language map invalid": {"name": "statement attachment \"description\" language map invalid"}, "statement substatement verb \"display\" language map invalid": {"name": "statement substatement verb \"display\" language map invalid"}, "statement substatement activity \"name\" language map invalid": {"name": "statement substatement activity \"name\" language map invalid"}, "statement substatement activity \"description\" language map invalid": {"name": "statement substatement activity \"description\" language map invalid"}, "Objects Verify Templates": {"name": "Objects Verify Templates"}, "should pass statement activity template": {"name": "should pass statement activity template"}, "should pass statement substatement activity template": {"name": "should pass statement substatement activity template"}, "should pass statement agent template": {"name": "should pass statement agent template"}, "should pass statement substatement agent template": {"name": "should pass statement substatement agent template"}, "should pass statement group template": {"name": "should pass statement group template"}, "should pass statement substatement group template": {"name": "should pass statement substatement group template"}, "should pass statement StatementRef template": {"name": "should pass statement StatementRef template"}, "should pass statement substatement StatementRef template": {"name": "should pass statement substatement StatementRef template"}, "should pass statement SubStatement template": {"name": "should pass statement SubStatement template"}, "An \"object\" property's \"objectType\" property is either \"Activity\", \"Agent\", \"Group\", \"SubStatement\", or \"StatementRef\"": {"name": "An \"object\" property's \"objectType\" property is either \"Activity\", \"Agent\", \"Group\", \"SubStatement\", or \"StatementRef\"", "1.0.2_ref_text": "Vocabulary, 4.1.4.b", "1.0.2_ref": "4.1.4.b", "1.0.3_ref": "Data 2.4.4.s2", "1.0.3_link": ["Data.md#2.4.4.s2"]}, "statement activity should fail on \"activity\"": {"name": "statement activity should fail on \"activity\""}, "statement substatement activity should fail on \"activity\"": {"name": "statement substatement activity should fail on \"activity\""}, "statement agent template should fail on \"agent\"": {"name": "statement agent template should fail on \"agent\""}, "statement substatement agent should fail on \"agent\"": {"name": "statement substatement agent should fail on \"agent\""}, "statement group should fail on \"group\"": {"name": "statement group should fail on \"group\""}, "statement substatement group should fail on \"group\"": {"name": "statement substatement group should fail on \"group\""}, "statement StatementRef should fail on \"statementref\"": {"name": "statement StatementRef should fail on \"statementref\""}, "statement substatement StatementRef should fail on \"statementref\"": {"name": "statement substatement StatementRef should fail on \"statementref\""}, "statement SubStatement should fail on \"substatement\"": {"name": "statement SubStatement should fail on \"substatement\""}, "Results Verify Templates": {"name": "Results Verify Templates"}, "should pass statement result template": {"name": "should pass statement result template"}, "should pass substatement result template": {"name": "should pass substatement result template"}, "A \"score\" property is an Object": {"name": "A \"score\" property is an Object", "1.0.2_ref_text": "Type, 4.1.5.table.row1.a", "1.0.2_ref": "4.1.5.table1.row1.a", "1.0.3_ref": "Data 2.4.5.1", "1.0.3_link": ["Data.md#2.4.5.1"]}, "statement result score numeric": {"name": "statement result score numeric"}, "statement result score string": {"name": "statement result score string"}, "statement substatement result score numeric": {"name": "statement substatement result score numeric"}, "statement substatement result score string": {"name": "statement substatement result score string"}, "A \"score\" Object's \"scaled\" property is a Decimal accurate to seven significant decimal figures": {"name": "A \"score\" Object's \"scaled\" property is a Decimal accurate to seven significant decimal figures", "1.0.2_ref_text": "Type, 4.1.5.1.table1.row1.a, SCORM 2004 4Ed", "1.0.2_ref": "4.1.5.1.table1.row1.a, SCORM 2004 4Ed", "1.0.3_ref": "Data 2.4.5.1.s2.table1.row1", "1.0.3_link": ["Data.md#2.4.5.1.s2.table1.row1"]}, "statement result \"scaled\" accepts seven significant decimal": {"name": "statement result \"scaled\" accepts seven significant decimal"}, "statement substatement result \"scaled\" accepts seven significant decimal": {"name": "statement substatement result \"scaled\" accepts seven significant decimal"}, "A \"score\" Object's \"raw\" property is a Decimal accurate to seven significant decimal figures": {"name": "A \"score\" Object's \"raw\" property is a Decimal accurate to seven significant decimal figures", "1.0.2_ref_text": "Type, 4.1.5.1.table1.row2.a, SCORM 2004 4Ed", "1.0.2_ref": "4.1.5.1.table1.row2.a, SCORM 2004 4Ed", "1.0.3_ref": "Data 2.4.5.1.s2.table1.row2", "1.0.3_link": ["Data.md#2.4.5.1.s2.table1.row2"]}, "statement result \"raw\" accepts seven significant decimal": {"name": "statement result \"raw\" accepts seven significant decimal"}, "statement substatement result \"raw\" accepts seven significant decimal": {"name": "statement substatement result \"raw\" accepts seven significant decimal"}, "A \"score\" Object's \"min\" property is a Decimal accurate to seven significant decimal figures": {"name": "A \"score\" Object's \"min\" property is a Decimal accurate to seven significant decimal figures", "1.0.2_ref_text": "Type, 4.1.5.1.table1.row3.a, SCORM 2004 4Ed", "1.0.2_ref": "4.1.5.1.table1.row3.a, SCORM 2004 4Ed", "1.0.3_ref": "Data 2.4.5.1.s2.table1.row3", "1.0.3_link": ["Data.md#2.4.5.1.s2.table1.row3"]}, "statement result \"min\" accepts seven significant decimal": {"name": "statement result \"min\" accepts seven significant decimal"}, "statement substatement result \"min\" accepts seven significant decimal": {"name": "statement substatement result \"min\" accepts seven significant decimal"}, "A \"score\" Object's \"max\" property is a Decimal accurate to seven significant decimal figures": {"name": "A \"score\" Object's \"max\" property is a Decimal accurate to seven significant decimal figures", "1.0.2_ref_text": "Type, 4.1.5.1.table1.row4.a, SCORM 2004 4Ed", "1.0.2_ref": "4.1.5.1.table1.row4.a, SCORM 2004 4Ed", "1.0.3_ref": "Data 2.4.5.1.s2.table1.row4", "1.0.3_link": ["Data.md#2.4.5.1.s2.table1.row4"]}, "statement result \"max\" accepts seven significant decimal": {"name": "statement result \"max\" accepts seven significant decimal"}, "statement substatement result \"max\" accepts seven significant decimal": {"name": "statement substatement result \"max\" accepts seven significant decimal"}, "A \"success\" property is a Boolean": {"name": "A \"success\" property is a Boolean", "1.0.2_ref_text": "Type, 4.1.5.table1.row2.a", "1.0.2_ref": "4.1.5.table1.row2.a", "1.0.3_ref": "Data 2.4.5.s2.table1.row1", "1.0.3_link": ["Data.md#2.4.5.s2.table1.row1"]}, "statement result \"success\" property is string \"true\"": {"name": "statement result \"success\" property is string \"true\""}, "statement result \"success\" property is string \"false\"": {"name": "statement result \"success\" property is string \"false\""}, "statement substatement result \"success\" property is string \"true\"": {"name": "statement substatement result \"success\" property is string \"true\""}, "statement substatement result \"success\" property is string \"false\"": {"name": "statement substatement result \"success\" property is string \"false\""}, "A \"completion\" property is a Boolean": {"name": "A \"completion\" property is a Boolean", "1.0.2_ref_text": "Type, 4.1.5.table1.row3.a", "1.0.2_ref": "4.1.5.table1.row3.a", "1.0.3_ref": "Data 2.4.5.s2.table1.row2", "1.0.3_link": ["Data.md#2.4.5.s2.table1.row2"]}, "statement result \"completion\" property is string \"true\"": {"name": "statement result \"completion\" property is string \"true\""}, "statement result \"completion\" property is string \"false\"": {"name": "statement result \"completion\" property is string \"false\""}, "statement substatement result \"completion\" property is string \"true\"": {"name": "statement substatement result \"completion\" property is string \"true\""}, "statement substatement result \"completion\" property is string \"false\"": {"name": "statement substatement result \"completion\" property is string \"false\""}, "A \"response\" property is a String": {"name": "A \"response\" property is a String", "1.0.2_ref_text": "Type, 4.1.5.table1.row3.a", "1.0.2_ref": "4.1.5.table1.row4.a", "1.0.3_ref": "Data 2.4.5.s2.table1.row3", "1.0.3_link": ["Data.md#2.4.5.s2.table1.row3"]}, "statement result \"response\" property is numeric": {"name": "statement result \"response\" property is numeric"}, "statement result \"completion\" property is object": {"name": "statement result \"completion\" property is object"}, "statement substatement result \"completion\" property is numeric": {"name": "statement substatement result \"completion\" property is numeric"}, "statement substatement result \"completion\" property is object": {"name": "statement substatement result \"completion\" property is object"}, "A \"duration\" property is a formatted to ISO 8601": {"name": "A \"duration\" property is a formatted to ISO 8601", "1.0.2_ref_text": "Type, 4.1.5.table1.row3.a", "1.0.2_ref": "4.1.5.table1.row5.a", "1.0.3_ref": "Data 2.4.5.s2.table1.row4", "1.0.3_link": ["Data.md#2.4.5.s2.table1.row4"]}, "statement result \"duration\" property is invalid": {"name": "statement result \"duration\" property is invalid"}, "statement substatement result \"duration\" property is invalid": {"name": "statement substatement result \"duration\" property is invalid"}, "A \"duration\" property keeps at least 0.01 seconds of precision": {"name": "A \"duration\" property keeps at least 0.01 seconds of precision", "1.0.2_ref_text": "Type, 4.1.5.table1.row3.a", "1.0.2_ref": "4.1.5.table1.row5.a", "1.0.3_ref": "Data 2.4.5.s2.table1.row4", "1.0.3_link": ["Data.md#2.4.5.s2.table1.row4"]}, "An \"extensions\" property is an Object": {"name": "An \"extensions\" property is an Object", "1.0.2_ref_text": "Type, 4.1.5.table1.row3.a", "1.0.2_ref": "4.1.5.table1.row6.a", "1.0.3_ref": "Data 2.4.5.s2.table1.row6", "1.0.3_link": ["Data.md#2.4.5.s2.table1.row6"]}, "statement result \"extensions\" property is numeric": {"name": "statement result \"extensions\" property is numeric"}, "statement result \"extensions\" property is string": {"name": "statement result \"extensions\" property is string"}, "statement substatement result \"extensions\" property is numeric": {"name": "statement substatement result \"extensions\" property is numeric"}, "statement substatement result \"extensions\" property is string": {"name": "statement substatement result \"extensions\" property is string"}, "An LRS stores 32-bit floating point numbers with at least the precision of IEEE 754": {"name": "An LRS stores 32-bit floating point numbers with at least the precision of IEEE 754", "1.0.2_ref_text": "4.1.12.d.a", "1.0.2_ref": "4.1.12.g", "1.0.3_ref": "Data 2.2.s4.b3", "1.0.3_link": ["Data.md#2.2.s4.b3"]}, "An LRS rejects with error code 400 Bad Request a Statement which uses the wrong data type": {"name": "An LRS rejects with error code 400 Bad Request a Statement which uses the wrong data type", "1.0.3_ref": "Data 2.2.s4.b1.b2", "1.0.3_link": ["Data.md#2.2.s4.b1.b2"]}, "with strings where numbers are required": {"name": "with strings where numbers are required"}, "even if those stings contain numbers": {"name": "even if those stings contain numbers"}, "with strings where booleans are required": {"name": "with strings where booleans are required"}, "even if thise string contain booleans": {"name": "even if thise string contain booleans"}, "StatementRefs Verify Templates": {"name": "StatementRefs Verify Templates"}, "should pass substatement StatementRef template": {"name": "should pass substatement StatementRef template"}, "A Statement Reference is defined by the \"objectType\" of an \"object\" with value \"StatementRef\"": {"name": "A Statement Reference is defined by the \"objectType\" of an \"object\" with value \"StatementRef\"", "1.0.2_ref_text": "*******.a", "1.0.2_ref": "*******.a", "1.0.3_ref": "Data *******.s4.b1", "1.0.3_link": ["Data.md#*******.s4.b1"]}, "statementref invalid when not \"StatementRef\"": {"name": "statementref invalid when not \"StatementRef\""}, "substatement statementref invalid when not \"StatementRef\"": {"name": "substatement statementref invalid when not \"StatementRef\""}, "A Statement Reference contains an \"id\" property": {"name": "A Statement Reference contains an \"id\" property", "1.0.2_ref_text": "Multiplicity, *******.table1.row2.b", "1.0.2_ref": "*******.table1.row2.b", "1.0.3_ref": "Data *******.s4.table1.row2", "1.0.3_link": ["Data.md#*******.s4.table1.row2"]}, "statementref invalid when missing \"id\"": {"name": "statementref invalid when missing \"id\""}, "substatement statementref invalid when missing \"id\"": {"name": "substatement statementref invalid when missing \"id\""}, "A Statement Reference's \"id\" property is a UUID": {"name": "A Statement Reference's \"id\" property is a UUID", "1.0.2_ref_text": "Type, *******.table1.row2.a", "1.0.2_ref": "*******.table1.row2.a", "1.0.3_ref": "Data *******.s4.table1.row2", "1.0.3_link": ["Data.md#*******.s4.table1.row2"]}, "statementref \"id\" not \"uuid\"": {"name": "statementref \"id\" not \"uuid\""}, "substatement statementref \"id\" not \"uuid\"": {"name": "substatement statementref \"id\" not \"uuid\""}, "Statements Verify Templates": {"name": "Statements Verify Templates"}, "should pass statement template": {"name": "should pass statement template"}, "A Statement contains an \"actor\" property": {"name": "A Statement contains an \"actor\" property", "1.0.2_ref_text": "Multiplicity, 4.1.b", "1.0.2_ref": "4.1.b", "1.0.3_ref": "Data 2.2.s2.b3", "1.0.3_link": ["Data.md#2.2.s2.b3"]}, "statement \"actor\" missing": {"name": "statement \"actor\" missing"}, "A Statement contains a \"verb\" property": {"name": "A Statement contains a \"verb\" property", "1.0.2_ref_text": "Multiplicity, 4.1.b", "1.0.2_ref": "4.1.b", "1.0.3_ref": "Data 2.2.s2.b3", "1.0.3_link": ["Data.md#2.2.s2.b3"]}, "statement \"verb\" missing": {"name": "statement \"verb\" missing"}, "A Statement contains an \"object\" property": {"name": "A Statement contains an \"object\" property", "1.0.2_ref_text": "Multiplicity, 4.1.b", "1.0.2_ref": "4.1.b", "1.0.3_ref": "Data 2.2.s2.b3", "1.0.3_link": ["Data.md#2.2.s2.b3"]}, "statement \"object\" missing": {"name": "statement \"object\" missing"}, "A Statement's \"id\" property is a String": {"name": "A Statement's \"id\" property is a String", "1.0.2_ref_text": "Type, 4.1.1.description.a", "1.0.2_ref": "4.1.1.description", "1.0.3_ref": "Data 2.4.1.s1", "1.0.3_link": ["Data.md#2.4.1.s1"]}, "statement \"id\" invalid numeric": {"name": "statement \"id\" invalid numeric"}, "statement \"id\" invalid object": {"name": "statement \"id\" invalid object"}, "A Statement's \"id\" property is a UUID following RFC 4122": {"name": "A Statement's \"id\" property is a UUID following RFC 4122", "1.0.2_ref_text": "Syntax, RFC 4122", "1.0.2_ref": "4.1.1.description, RFC 4122", "1.0.3_ref": "Data 2.4.1.s1, RFC 4122", "1.0.3_link": ["Data.md#2.4.1.s1"]}, "statement \"id\" invalid UUID with too many digits": {"name": "statement \"id\" invalid UUID with too many digits"}, "statement \"id\" invalid UUID with non A-F": {"name": "statement \"id\" invalid UUID with non A-F"}, "A TimeStamp is defined as a Date/Time formatted according to ISO 8601": {"name": "A TimeStamp is defined as a Date/Time formatted according to ISO 8601", "1.0.2_ref_text": "Format, ISO8601", "1.0.2_ref": "4.1.table1.row7.b, ISO8601", "1.0.3_ref": "Data 4.5.s1.b1, ISO8601", "1.0.3_link": ["Data.md#4.5.s1.b1"]}, "statement \"template\" invalid string": {"name": "statement \"template\" invalid string"}, "statement \"template\" invalid date": {"name": "statement \"template\" invalid date"}, "A \"timestamp\" property is a TimeStamp": {"name": "A \"timestamp\" property is a TimeStamp", "1.0.2_ref_text": "Type, *******.table1.row7.a, *******.table1.row7.b", "1.0.2_ref": "4.1.table1.row7.a, 4.1.table1.row7.b", "1.0.3_ref": "Data 2.4.s1.table1.row7", "1.0.3_link": ["Data.md#2.4.s1.table1.row7"]}, "A \"stored\" property is a TimeStamp": {"name": "A \"stored\" property is a TimeStamp", "1.0.2_ref_text": "Type, *******.table1.row8.a, *******.table1.row8.b", "1.0.2_ref": "4.1.table1.row8.a", "1.0.3_ref": "Data 2.4.s1.table1.row8", "1.0.3_link": ["Data.md#2.4.s1.table1.row8"]}, "statement \"stored\" invalid string": {"name": "statement \"stored\" invalid string"}, "statement \"stored\" invalid date": {"name": "statement \"stored\" invalid date"}, "A \"version\" property enters the LRS with the value of \"1.0.0\" or is not used": {"name": "A \"version\" property enters the LRS with the value of \"1.0.0\" or is not used", "1.0.2_ref_text": "Vocabulary, 4.1.10.e, 4.1.10.f", "1.0.2_ref": "4.1.10.e", "1.0.3_ref": "Data *******.s5.b3", "1.0.3_link": ["Data.md#*******.s5.b3"]}, "statement \"version\" invalid string": {"name": "statement \"version\" invalid string"}, "statement \"version\" invalid": {"name": "statement \"version\" invalid"}, "An LRS rejects with error code 400 Bad Request any Statement having a property whose value is set to \"null\", except in an \"extensions\" property": {"name": "An LRS rejects with error code 400 Bad Request any Statement having a property whose value is set to \"null\", except in an \"extensions\" property", "1.0.2_ref_text": "4.1.12.d.a", "1.0.2_ref": "4.1.12.d.a", "1.0.3_ref": "Data 2.2.s4.b1.b1", "1.0.3_link": ["Data.md#2.2.s4.b1.b1"]}, "statement actor should fail on \"null\"": {"name": "statement actor should fail on \"null\""}, "statement verb should fail on \"null\"": {"name": "statement verb should fail on \"null\""}, "statement context should fail on \"null\"": {"name": "statement context should fail on \"null\""}, "statement object should fail on \"null\"": {"name": "statement object should fail on \"null\""}, "An LRS rejects with error code 400 Bad Request, a Request which uses \"version\" and has the value set to anything but \"1.0\" or \"1.0.x\", where x is the semantic versioning number": {"name": "An LRS rejects with error code 400 Bad Request, a Request which uses \"version\" and has the value set to anything but \"1.0\" or \"1.0.x\", where x is the semantic versioning number", "1.0.2_ref_text": "Format, 4.1.10.b, 6.2.c, 6.2.f", "1.0.2_ref": "4.1.10.b, 4.1.10.c, 6.2.c, 6.2.f", "1.0.3_ref": "Data 2.4.10.s2.b1, Data 2.4.10.s3.b1, Communication 3.3.s3.b3, Communication 3.3.s3.b6", "1.0.3_link": ["Data.md#2.4.10.s2.b1", "Data.md#2.4.10.s3.b1", "Communication.md#3.3.s3.b3", "Communication.md#3.3.s3.b6"]}, "statement \"version\" valid 1.0": {"name": "statement \"version\" valid 1.0"}, "statement \"version\" valid 1.0.9": {"name": "statement \"version\" valid 1.0.9"}, "statement \"version\" invalid 0.9.9": {"name": "statement \"version\" invalid 0.9.9"}, "statement \"version\" invalid 1.1.0": {"name": "statement \"version\" invalid 1.1.0"}, "An LRS rejects with error code 400 Bad Request, a Request which the \"X-Experience-API-Version\" header's value is anything but \"1.0\" or \"1.0.x\", where x is the semantic versioning number to any Resource except the About Resource": {"name": "An LRS rejects with error code 400 Bad Request, a Request which the \"X-Experience-API-Version\" header's value is anything but \"1.0\" or \"1.0.x\", where x is the semantic versioning number to any Resource except the About Resource", "1.0.2_ref_text": "Format, 6.2.d, 6.2.e, 6.2.f, 7.7.f", "1.0.2_ref": "6.2.d, 6.2.e, 6.2.f, 7.7.c", "1.0.3_ref": "Communication 3.3.s3.b4, Communication 3.3.s3.b6", "1.0.3_link": ["Communication.md#3.3.s3.b4", "Communication.md#3.3.s3.b6"]}, "An LRS rejects with error code 400 Bad Request any Statement violating a Statement Requirement.": {"name": "An LRS rejects with error code 400 Bad Request any Statement violating a Statement Requirement.", "1.0.2_ref_text": "4.1.12, <PERSON><PERSON>", "1.0.2_ref": "4.1.12, <PERSON><PERSON>", "1.0.3_ref": "Data 2.2.s4, <PERSON><PERSON>", "1.0.3_link": ["Data.md#2.2.s4"]}, "statement \"actor\" missing reply 400": {"name": "statement \"actor\" missing reply 400"}, "statement \"verb\" missing reply 400": {"name": "statement \"verb\" missing reply 400"}, "statement \"object\" missing reply 400": {"name": "statement \"object\" missing reply 400"}, "SubStatements Verify Templates": {"name": "SubStatements Verify Templates"}, "A Sub-Statement is defined by the \"objectType\" of an \"object\" with value \"SubStatement\"": {"name": "A Sub-Statement is defined by the \"objectType\" of an \"object\" with value \"SubStatement\"", "1.0.2_ref_text": "*******.d", "1.0.2_ref": "*******.d", "1.0.3_ref": "Data *******.s8.b1", "1.0.3_link": ["Data.md#*******.s8.b1"]}, "substatement invalid when not \"SubStatement\"": {"name": "substatement invalid when not \"SubStatement\""}, "A Sub-Statement follows the requirements of all Statements": {"name": "A Sub-Statement follows the requirements of all Statements", "1.0.2_ref_text": "*******.e", "1.0.2_ref": "*******.e", "1.0.3_ref": "Data *******.s8.b2", "1.0.3_link": ["Data.md#*******.s8.b2"]}, "substatement requires actor": {"name": "substatement requires actor"}, "substatement requires object": {"name": "substatement requires object"}, "substatement requires verb": {"name": "substatement requires verb"}, "should pass substatement context": {"name": "should pass substatement context"}, "should pass substatement result": {"name": "should pass substatement result"}, "should pass substatement statementref": {"name": "should pass substatement statementref"}, "should pass substatement as agent": {"name": "should pass substatement as agent"}, "should pass substatement as group": {"name": "should pass substatement as group"}, "A Sub-Statement cannot have a Sub-Statement": {"name": "A Sub-Statement cannot have a Sub-Statement", "1.0.2_ref_text": "*******.g", "1.0.2_ref": "*******.g", "1.0.3_ref": "Data *******.s8.b4", "1.0.3_link": ["Data.md#*******.s8.b4"]}, "substatement invalid nested \"SubStatement\"": {"name": "substatement invalid nested \"SubStatement\""}, "A Sub-Statement cannot use the \"id\" property at the Statement level": {"name": "A Sub-Statement cannot use the \"id\" property at the Statement level", "1.0.2_ref_text": "*******.f", "1.0.2_ref": "*******.f", "1.0.3_ref": "Data *******.s8.b3", "1.0.3_link": ["Data.md#*******.s8.b3"]}, "substatement invalid with property \"id\"": {"name": "substatement invalid with property \"id\""}, "A Sub-Statement cannot use the \"stored\" property": {"name": "A Sub-Statement cannot use the \"stored\" property", "1.0.2_ref_text": "*******.f", "1.0.2_ref": "*******.f", "1.0.3_ref": "Data *******.s8.b3", "1.0.3_link": ["Data.md#*******.s8.b3"]}, "An LRS MUST accept statements with the stored property": {"name": "An LRS MUST accept statements with the stored property", "1.0.3_ref": "Data 2.4.8.s3.b2", "1.0.3_link": ["Data.md#2.4.8.s3.b2"]}, "A stored property must be a TimeStamp": {"name": "A stored property must be a TimeStamp", "1.0.3_ref": "Data 2.4.8.s2", "1.0.3_link": ["Data.md#2.4.8.s2"]}, "substatement invalid with property \"stored\"": {"name": "substatement invalid with property \"stored\""}, "A Sub-Statement cannot use the \"version\" property": {"name": "A Sub-Statement cannot use the \"version\" property", "1.0.2_ref_text": "*******.f", "1.0.2_ref": "*******.f", "1.0.3_ref": "Data *******.s8.b3", "1.0.3_link": ["Data.md#*******.s8.b3"]}, "substatement invalid with property \"version\"": {"name": "substatement invalid with property \"version\""}, "A Sub-Statement cannot use the \"authority\" property": {"name": "A Sub-Statement cannot use the \"authority\" property", "1.0.2_ref_text": "*******.f", "1.0.2_ref": "*******.f", "1.0.3_ref": "Data *******.s8.b3", "1.0.3_link": ["Data.md#*******.s8.b3"]}, "substatement invalid with property \"authority\"": {"name": "substatement invalid with property \"authority\""}, "All UUID types follow requirements of RFC4122": {"name": "All UUID types follow requirements of RFC4122", "1.0.2_ref_text": "Type, 4.1.1", "1.0.2_ref": "4.1.1", "1.0.3_ref": "Data 2.4.1.s1", "1.0.3_link": ["Data.md#2.4.1.s1"]}, "statement object statementref \"id\" invalid UUID with too many digits": {"name": "statement object statementref \"id\" invalid UUID with too many digits"}, "statement object statementref \"id\" invalid UUID with non A-F": {"name": "statement object statementref \"id\" invalid UUID with non A-F"}, "statement context \"registration\" invalid UUID with too many digits": {"name": "statement context \"registration\" invalid UUID with too many digits"}, "statement context \"registration\" invalid UUID with non A-F": {"name": "statement context \"registration\" invalid UUID with non A-F"}, "statement context \"statement\" invalid UUID with too many digits": {"name": "statement context \"statement\" invalid UUID with too many digits"}, "statement substatement context \"statement\" invalid UUID with non A-F": {"name": "statement substatement context \"statement\" invalid UUID with non A-F"}, "All UUID types are in standard String form": {"name": "All UUID types are in standard String form", "1.0.2_ref_text": "Type, 4.1.1", "1.0.2_ref": "4.1.1", "1.0.3_ref": "Data 2.4.1.s1", "1.0.3_link": ["Data.md#2.4.1.s1"]}, "statement object statementref \"id\" invalid numeric": {"name": "statement object statementref \"id\" invalid numeric"}, "statement object statementref \"id\" invalid object": {"name": "statement object statementref \"id\" invalid object"}, "statement context \"registration\" invalid numeric": {"name": "statement context \"registration\" invalid numeric"}, "statement context \"registration\" invalid object": {"name": "statement context \"registration\" invalid object"}, "statement context \"statement\" invalid numeric": {"name": "statement context \"statement\" invalid numeric"}, "statement substatement context \"statement\" invalid object": {"name": "statement substatement context \"statement\" invalid object"}, "Verbs Verify Templates": {"name": "Verbs Verify Templates"}, "should pass substatement verb template": {"name": "should pass substatement verb template"}, "A \"verb\" property contains an \"id\" property": {"name": "A \"verb\" property contains an \"id\" property", "1.0.2_ref_text": "Multiplicity, 4.1.3.table1.row1.b", "1.0.2_ref": "4.1.3.table1.row1.b", "1.0.3_ref": "Data 2.4.3.s3.table1.row1", "1.0.3_link": ["Data.md#2.4.3.s3.table1.row1"]}, "statement verb missing \"id\"": {"name": "statement verb missing \"id\""}, "statement substatement verb missing \"id\"": {"name": "statement substatement verb missing \"id\""}, "A \"verb\" property's \"id\" property is an IRI": {"name": "A \"verb\" property's \"id\" property is an IRI", "1.0.2_ref_text": "Type, 4.1.3.table1.row1.a", "1.0.2_ref": "4.1.3.table1.row1.a", "1.0.3_ref": "Data 2.4.3.s3.table1.row1", "1.0.3_link": ["Data.md#2.4.3.s3.table1.row1"]}, "statement verb \"id\" not IRI": {"name": "statement verb \"id\" not IRI"}, "statement substatement verb \"id\" not IRI": {"name": "statement substatement verb \"id\" not IRI"}, "A Voiding Statement is defined as a Statement whose \"verb\" property's \"id\" property's IRI ending with \"voided\"": {"name": "A Voiding Statement is defined as a Statement whose \"verb\" property's \"id\" property's IRI ending with \"voided\"", "1.0.2_ref_text": "4.3", "1.0.2_ref": "4.3", "1.0.3_ref": "Data 2.3.2", "1.0.3_link": ["Data.md#2.3.2"]}, "statement verb voided IRI ends with \"voided\" (WARNING: this applies \"Upon receiving a Statement that voids another, the LRS SHOULD NOT* reject the request on the grounds of the Object of that voiding Statement not being present\")": {"name": "statement verb voided IRI ends with \"voided\" (WARNING: this applies \"Upon receiving a Statement that voids another, the LRS SHOULD NOT* reject the request on the grounds of the Object of that voiding Statement not being present\")"}, "A Voiding Statement's \"objectType\" field has a value of \"StatementRef\"": {"name": "A Voiding Statement's \"objectType\" field has a value of \"StatementRef\"", "1.0.2_ref_text": "Format, 4.3.a", "1.0.2_ref": "4.3.a", "1.0.3_ref": "Data 2.3.2.s2.b1", "1.0.3_link": ["Data.md#2.3.2.s2.b1"]}, "statement verb voided uses substatement with \"StatementRef\"": {"name": "statement verb voided uses substatement with \"StatementRef\""}, "statement verb voided does not use object \"StatementRef\"": {"name": "statement verb voided does not use object \"StatementRef\""}, "A \"verb\" property's \"display\" property is a Language Map": {"name": "A \"verb\" property's \"display\" property is a Language Map", "1.0.2_ref_text": "Type, 4.1.3.table1.row2.a", "1.0.2_ref": "4.1.3.table1.row2.a", "1.0.3_ref": "Data 2.4.3.s3.table1.row2", "1.0.3_link": ["Data.md#2.4.3.s3.table1.row2"]}, "statement verb \"display\" not language": {"name": "statement verb \"display\" not language"}, "statement substatement verb \"display\" not language": {"name": "statement substatement verb \"display\" not language"}, "Document Resource Requirements": {"name": "Document Resource Requirements", "1.0.3_ref": ["Communication 2.2"], "1.0.3_link": ["Communication.md#2.2"]}, "An LRS has a State Resource with endpoint \"base IRI\"+\"/activities/state\"": {"name": "An LRS has a State Resource with endpoint \"base IRI\"+\"/activities/state\"", "1.0.2_ref_text": "7.3.table1.row1.a ,7.3.table1.row1.c", "1.0.2_ref": "7.3.table1.row1.b ,7.3.table1.row1.c", "1.0.3_ref": "Communication 2.2.table2.row1.a, Communication 2.2.table2.row1.c", "1.0.3_link": ["Communication.md#2.2.table2.row1.a", "Communication.md#2.2.table2.row1.c"]}, "An LRS has an Activities Resource with endpoint \"base IRI\" + /activities\"": {"name": "An LRS has an Activities Resource with endpoint \"base IRI\" + /activities\"", "1.0.2_ref_text": "7.5", "1.0.2_ref": "7.5, Implicit", "1.0.3_ref": "Communication 2.5", "1.0.3_link": ["Communication.md#2.5"]}, "An LRS has an Activity Profile Resource with endpoint \"base IRI\"+\"/activities/profile\"": {"name": "An LRS has an Activity Profile Resource with endpoint \"base IRI\"+\"/activities/profile\"", "1.0.2_ref_text": "7.3.table1.row2.a, 7.3.table1.row2.c", "1.0.2_ref": "7.3.table1.row2.b, 7.3.table1.row2.c", "1.0.3_ref": "Communication 2.2.table2.row2.a, Communication 2.2.table2.row2.c", "1.0.3_link": ["Communication.md#2.2.table2.row2.a", "Communication.md#2.2.table2.row2.c"]}, "An Activity Definition uses the \"interactionType\" property if any of the correctResponsesPattern, choices, scale, source, target, or steps properties are used": {"name": "An Activity Definition uses the \"interactionType\" property if any of the correctResponsesPattern, choices, scale, source, target, or steps properties are used", "1.0.3_ref": "Data *******.s8", "1.0.3_link": ["Data.md#*******.s8"]}, "An LRS has an Agents Resource with endpoint \"base IRI\" + /agents\"": {"name": "An LRS has an Agents Resource with endpoint \"base IRI\" + /agents\"", "1.0.2_ref_text": "7.6", "1.0.2_ref": "7.6, I<PERSON><PERSON>", "1.0.3_ref": "Communication 2.4", "1.0.3_link": ["Communication.md#2.4"]}, "An LRS has an Agent Profile Resource with endpoint \"base IRI\"+\"/agents/profile\"": {"name": "An LRS has an Agent Profile Resource with endpoint \"base IRI\"+\"/agents/profile\"", "1.0.2_ref_text": "7.3.table1.row3.a, 7.3.table1.row3.c", "1.0.2_ref": "7.3.table1.row3.b, 7.3.table1.row3.c", "1.0.3_ref": "Communication 2.2.table2.row3.a, Communication 2.2.table2.row3.c", "1.0.3_link": ["Communication.md#2.2.table2.row3.a", "Communication.md#2.2.table2.row3.c"]}, "An LRS has an About Resource with endpoint \"base IRI\"+\"/about\"": {"name": "An LRS has an About Resource with endpoint \"base IRI\"+\"/about\"", "1.0.2_ref_text": "7.7.a", "1.0.2_ref": "7.7.a", "1.0.3_ref": "Communication 2.8", "1.0.3_link": ["Communication.md#2.8"]}, "An LRS will accept a POST request to the State Resource": {"name": "An LRS will accept a POST request to the State Resource", "1.0.2_ref_text": "7.3.table1.row1.b", "1.0.2_ref": "7.3.table1.row1.a", "1.0.3_ref": "Communication 2.2.table2.row1.a", "1.0.3_link": ["Communication.md#2.2.table2.row1.a"]}, "An LRS will accept a POST request to the Activity Profile Resource": {"name": "An LRS will accept a POST request to the Activity Profile Resource", "1.0.2_ref_text": "7.3.table1.row2.b", "1.0.2_ref": "7.3.table1.row2.a", "1.0.3_ref": "Communication 2.2.table2.row2.a", "1.0.3_link": ["Communication.md#2.2.table2.row2.a"]}, "An LRS will accept a POST request to the Agent Profile Resource": {"name": "An LRS will accept a POST request to the Agent Profile Resource", "1.0.2_ref_text": "7.3.table1.row3.b", "1.0.2_ref": "7.3.table1.row3.a", "1.0.3_ref": "Communication 2.2.table2.row3.a", "1.0.3_link": ["Communication.md#2.2.table2.row3.a"]}, "An LRS cannot reject a POST request to the State Resource based on the contents of the name/value pairs of the document": {"name": "An LRS cannot reject a POST request to the State Resource based on the contents of the name/value pairs of the document", "1.0.2_ref_text": "7.3.b", "1.0.2_ref": "7.3.b, Implicit", "1.0.3_ref": "Communication 2.2.b, Implicit", "1.0.3_link": ["Communication.md#2.2.b"]}, "Should accept POST to State with document [object Object]": {"name": "Should accept POST to State with document [object Object]"}, "Should accept POST to State with document 1": {"name": "Should accept POST to State with document 1"}, "Should accept POST to State with document true": {"name": "Should accept POST to State with document true"}, "An LRS cannot reject a POST request to the Activity Profile Resource based on the contents of the name/value pairs of the document": {"name": "An LRS cannot reject a POST request to the Activity Profile Resource based on the contents of the name/value pairs of the document", "1.0.2_ref_text": "7.3.b", "1.0.2_ref": "7.3.b, Implicit", "1.0.3_ref": "Communication 2.2.b, Implicit", "1.0.3_link": ["Communication.md#2.2.b"]}, "Should accept POST to Activity profile with document [object Object]": {"name": "Should accept POST to Activity profile with document [object Object]"}, "Should accept POST to Activity profile with document 1": {"name": "Should accept POST to Activity profile with document 1"}, "Should accept POST to Activity profile with document true": {"name": "Should accept POST to Activity profile with document true"}, "An LRS cannot reject a POST request to the Agent Profile Resource based on the contents of the name/value pairs of the document": {"name": "An LRS cannot reject a POST request to the Agent Profile Resource based on the contents of the name/value pairs of the document", "1.0.2_ref_text": "7.3.b", "1.0.2_ref": "7.3.b, Implicit", "1.0.3_ref": "Communication 2.2.b, Implicit", "1.0.3_link": ["Communication.md#2.2.b"]}, "Should accept POST to Agent profile with document [object Object]": {"name": "Should accept POST to Agent profile with document [object Object]"}, "Should accept POST to Agent profile with document 1": {"name": "Should accept POST to Agent profile with document 1"}, "Should accept POST to Agent profile with document true": {"name": "Should accept POST to Agent profile with document true"}, "An LRS's State Resource, upon receiving a POST request for a document not currently in the LRS, treats it as a PUT request and store a new document": {"name": "An LRS's State Resource, upon receiving a POST request for a document not currently in the LRS, treats it as a PUT request and store a new document", "1.0.2_ref_text": "7.3.f", "1.0.2_ref": "7.3.f", "1.0.3_ref": "Communication 2.2.s7, Implicit", "1.0.3_link": ["Communication.md#2.2.s7"]}, "An LRS's State Resource, rejects a POST request if the document is found and either document's type is not \"application/json\" with error code 400 Bad Request": {"name": "An LRS's State Resource, rejects a POST request if the document is found and either document's type is not \"application/json\" with error code 400 Bad Request", "1.0.2_ref_text": "7.3.e", "1.0.2_ref": "7.3.e", "1.0.3_ref": "Communication 2.2.s8.b1", "1.0.3_link": ["Communication.md#2.2.s8.b1"]}, "A Document Merge overwrites any duplicate Objects from the previous document with the new document.": {"name": "A Document Merge overwrites any duplicate Objects from the previous document with the new document.", "1.0.2_ref_text": "7.3.d", "1.0.2_ref": "7.3.d", "1.0.3_ref": "Communication 2.2.s7.b1, Communication 2.2.s7.b2, Communication 2.2.s7.b3", "1.0.3_link": ["Communication.md#2.2.s7.b1", "Communication.md#2.2.s7.b2", "Communication.md#2.2.s7.b3"]}, "A Document Merge only performs overwrites at one level deep, although the entire object is replaced.": {"name": "A Document Merge only performs overwrites at one level deep, although the entire object is replaced.", "1.0.2_ref_text": "7.3.d", "1.0.2_ref": "7.3.d", "1.0.3_ref": "Communication 2.2.s7.b1, Communication 2.2.s7.b2, Communication 2.2.s7.b3", "1.0.3_link": ["Communication.md#2.2.s7.b1", "Communication.md#2.2.s7.b2", "Communication.md#2.2.s7.b3"]}, "An LRS's State Resource performs a Document Merge if a document is found and both it and the document in the POST request have type \"application/json\"": {"name": "An LRS's State Resource performs a Document Merge if a document is found and both it and the document in the POST request have type \"application/json\"", "1.0.2_ref_text": "7.3.d", "1.0.2_ref": "7.3.d", "1.0.3_ref": "Communication 2.2.s7.b1, Communication 2.2.s7.b2, Communication 2.2.s7.b3", "1.0.3_link": ["Communication.md#2.2.s7.b1", "Communication.md#2.2.s7.b2", "Communication.md#2.2.s7.b3"]}, "An LRS's Activity Profile Resource, upon receiving a POST request for a document not currently in the LRS, treats it as a PUT request and store a new document": {"name": "An LRS's Activity Profile Resource, upon receiving a POST request for a document not currently in the LRS, treats it as a PUT request and store a new document", "1.0.2_ref_text": "7.3.f", "1.0.2_ref": "7.3.f", "1.0.3_ref": "Communication 2.2.s7", "1.0.3_link": ["Communication.md#2.2.s7"]}, "An LRS's Activity Profile Resource, rejects a POST request if the document is found and either document's type is not \"application/json\" with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource, rejects a POST request if the document is found and either document's type is not \"application/json\" with error code 400 Bad Request", "1.0.2_ref_text": "7.3.e", "1.0.2_ref": "7.3.e", "1.0.3_ref": "Communication 2.2.s8.b1", "1.0.3_link": ["Communication.md#2.2.s8.b1"]}, "An LRS's Activity Profile Resource performs a Document Merge if a document is found and both it and the document in the POST request have type \"application/json\"": {"name": "An LRS's Activity Profile Resource performs a Document Merge if a document is found and both it and the document in the POST request have type \"application/json\"", "1.0.2_ref_text": "7.3.d", "1.0.2_ref": "7.3.d", "1.0.3_ref": "Communication 2.2.s7.b1, Communication 2.2.s7.b2, Communication 2.2.s7.b3", "1.0.3_link": ["Communication.md#2.2.s7.b1", "Communication.md#2.2.s7.b2", "Communication.md#2.2.s7.b3"]}, "An LRS's Agent Profile Resource, upon receiving a POST request for a document not currently in the LRS, treats it as a PUT request and store a new document": {"name": "An LRS's Agent Profile Resource, upon receiving a POST request for a document not currently in the LRS, treats it as a PUT request and store a new document", "1.0.2_ref_text": "7.3.f", "1.0.2_ref": "7.3.f", "1.0.3_ref": "Communication 2.2.s7", "1.0.3_link": ["Communication.md#2.2.s7"]}, "An LRS's Agent Profile Resource, rejects a POST request if the document is found and either document's type is not \"application/json\" with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource, rejects a POST request if the document is found and either document's type is not \"application/json\" with error code 400 Bad Request", "1.0.2_ref_text": "7.3.e", "1.0.2_ref": "7.3.e", "1.0.3_ref": "Communication 2.2.s8.b1", "1.0.3_link": ["Communication.md#2.2.s8.b1"]}, "An LRS's Agent Profile Resource performs a Document Merge if a document is found and both it and the document in the POST request have type \"application/json\"": {"name": "An LRS's Agent Profile Resource performs a Document Merge if a document is found and both it and the document in the POST request have type \"application/json\"", "1.0.2_ref_text": "7.3.d", "1.0.2_ref": "7.3.d", "1.0.3_ref": "Communication 2.2.s7.b1, Communication 2.2.s7.b2, Communication 2.2.s7.b3", "1.0.3_link": ["Communication.md#2.2.s7.b1", "Communication.md#2.2.s7.b2", "Communication.md#2.2.s7.b3"]}, "An LRS's State Resource accepts PUT requests": {"name": "An LRS's State Resource accepts PUT requests", "1.0.2_ref_text": "7.4", "1.0.2_ref": "7.4", "1.0.3_ref": "Communication 2.3", "1.0.3_link": ["Communication.md#2.3"]}, "An LRS's State Resource rejects a PUT request without \"activityId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a PUT request without \"activityId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.4.table1.row1.b", "1.0.2_ref": "7.4.table1.row1.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row1", "1.0.3_link": ["Communication.md#2.3.s3.table1.row1"]}, "An LRS's State Resource rejects a PUT request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a PUT request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row1.a", "1.0.2_ref": "7.4.table1.row1.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row1", "1.0.3_link": ["Communication.md#2.3.s3.table1.row1"]}, "Should State Resource reject a PUT request with activityId type [object Object]": {"name": "Should State Resource reject a PUT request with activityId type [object Object]"}, "Should State Resource reject a PUT request with activityId type 1": {"name": "Should State Resource reject a PUT request with activityId type 1"}, "Should State Resource reject a PUT request with activityId type true": {"name": "Should State Resource reject a PUT request with activityId type true"}, "Should State Resource reject a PUT request with activityId type undefined": {"name": "Should State Resource reject a PUT request with activityId type undefined"}, "An LRS's State Resource rejects a PUT request without \"agent\" as a parameter with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a PUT request without \"agent\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.4.table1.row2.b", "1.0.2_ref": "7.4.table1.row2.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row2", "1.0.3_link": ["Communication.md#2.3.s3.table1.row2"]}, "An LRS's State Resource rejects a PUT request with \"agent\" as a parameter if it is not in JSON format with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a PUT request with \"agent\" as a parameter if it is not in JSON format with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row2.a", "1.0.2_ref": "7.4.table1.row2.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row2", "1.0.3_link": ["Communication.md#2.3.s3.table1.row2"]}, "An LRS's State Resource can process a PUT request with \"registration\" as a parameter": {"name": "An LRS's State Resource can process a PUT request with \"registration\" as a parameter", "1.0.2_ref_text": "multiplicity, 7.4.table1.row3.b", "1.0.2_ref": "7.4.table1.row3.b", "1.0.3_link": ["multiplicity, Communication 2.3.s3.table1.row3, XAPI-00218"], "1.0.3_ref": ["Communication.md#2.3.s3.table1.row3"]}, "An LRS's State Resource rejects a PUT request with \"registration\" as a parameter if it is not a UUID with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a PUT request with \"registration\" as a parameter if it is not a UUID with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row3.a", "1.0.2_ref": "7.4.table1.row3.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row3", "1.0.3_link": ["Communication.md#2.3.s3.table1.row3"]}, "Should reject PUT with \"registration\" with type 1": {"name": "Should reject PUT with \"registration\" with type 1"}, "Should reject PUT with \"registration\" with type true": {"name": "Should reject PUT with \"registration\" with type true"}, "Should reject PUT with \"registration\" with type not UUID": {"name": "Should reject PUT with \"registration\" with type not UUID"}, "An LRS's State Resource rejects a PUT request without \"stateId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a PUT request without \"stateId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.4.table1.row1.b", "1.0.2_ref": "7.4.table1.row4.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row4", "1.0.3_link": ["Communication.md#2.3.s3.table1.row4"]}, "An LRS's State Resource upon processing a successful PUT request returns code 204 No Content": {"name": "An LRS's State Resource upon processing a successful PUT request returns code 204 No Content", "1.0.2_ref_text": "7.4.a", "1.0.2_ref": "7.4.a", "1.0.3_ref": "Communication 2.3.s3", "1.0.3_link": ["Communication.md#2.3.s3"]}, "An LRS's State Resource accepts POST requests": {"name": "An LRS's State Resource accepts POST requests", "1.0.2_ref_text": "7.4", "1.0.2_ref": "7.4", "1.0.3_ref": "Communication 2.3", "1.0.3_link": ["Communication.md#2.3"]}, "An LRS's State Resource rejects a POST request without \"activityId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a POST request without \"activityId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.4.table1.row1.b", "1.0.2_ref": "7.4.table1.row1.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row1", "1.0.3_link": ["Communication.md#2.3.s3.table1.row1"]}, "An LRS's State Resource rejects a POST request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a POST request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row1.a", "1.0.2_ref": "7.4.table1.row1.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row1", "1.0.3_link": ["Communication.md#2.3.s3.table1.row1"]}, "Should reject PUT State with stateId type : 1": {"name": "Should reject PUT State with stateId type : 1"}, "Should reject PUT State with stateId type : true": {"name": "Should reject PUT State with stateId type : true"}, "Should reject PUT State with stateId type : [object Object]": {"name": "Should reject PUT State with stateId type : [object Object]"}, "Should reject PUT State with stateId type : undefined": {"name": "Should reject PUT State with stateId type : undefined"}, "An LRS's State Resource rejects a POST request without \"agent\" as a parameter with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a POST request without \"agent\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.4.table1.row2.b", "1.0.2_ref": "7.4.table1.row2.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row2", "1.0.3_link": ["Communication.md#2.3.s3.table1.row2"]}, "An LRS's State Resource rejects a POST request with \"agent\" as a parameter if it is not in JSON format with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a POST request with \"agent\" as a parameter if it is not in JSON format with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row2.a", "1.0.2_ref": "7.4.table1.row2.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row2", "1.0.3_link": ["Communication.md#2.3.s3.table1.row2"]}, "Should reject POST State with agent type : 1": {"name": "Should reject POST State with agent type : 1"}, "Should reject POST State with agent type : true": {"name": "Should reject POST State with agent type : true"}, "Should reject POST State with agent type : not JSON": {"name": "Should reject POST State with agent type : not JSON"}, "Should reject POST State with agent type : undefined": {"name": "Should reject POST State with agent type : undefined"}, "An LRS's State Resource can process a POST request with \"registration\" as a parameter": {"name": "An LRS's State Resource can process a POST request with \"registration\" as a parameter", "1.0.2_ref_text": "multiplicity, 7.4.table1.row3.b", "1.0.2_ref": "7.4.table1.row3.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row3, XAPI-00227", "1.0.3_link": ["Communication.md#2.3.s3.table1.row3"]}, "An LRS's State Resource rejects a POST request with \"registration\" as a parameter if it is not a UUID with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a POST request with \"registration\" as a parameter if it is not a UUID with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row3.a", "1.0.2_ref": "7.4.table1.row3.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row3", "1.0.3_link": ["Communication.md#2.3.s3.table1.row3"]}, "Should reject POST with \"registration\" with type 1": {"name": "Should reject POST with \"registration\" with type 1"}, "Should reject POST with \"registration\" with type true": {"name": "Should reject POST with \"registration\" with type true"}, "Should reject POST with \"registration\" with type not UUID": {"name": "Should reject POST with \"registration\" with type not UUID"}, "An LRS's State Resource rejects a POST request without \"stateId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a POST request without \"stateId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.4.table1.row1.b", "1.0.2_ref": "7.4.table1.row4.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row4", "1.0.3_link": ["Communication.md#2.3.s3.table1.row4"]}, "An LRS's State Resource upon processing a successful POST request returns code 204 No Content": {"name": "An LRS's State Resource upon processing a successful POST request returns code 204 No Content", "1.0.2_ref_text": "7.4.a", "1.0.2_ref": "7.4.a", "1.0.3_ref": "Communication 2.3.s3", "1.0.3_link": ["Communication.md#2.3.s3"]}, "An LRS's State Resource accepts GET requests": {"name": "An LRS's State Resource accepts GET requests", "1.0.2_ref_text": "7.4", "1.0.2_ref": "7.4", "1.0.3_ref": "Communication 2.3", "1.0.3_link": ["Communication.md#2.3"]}, "An LRS's State Resource rejects a GET request without \"activityId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a GET request without \"activityId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.4.table1.row1.b", "1.0.2_ref": "7.4.table1.row1.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row1", "1.0.3_link": ["Communication.md#2.3.s3.table1.row1"]}, "An LRS's State Resource rejects a GET request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a GET request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row1.a", "1.0.2_ref": "7.4.table1.row1.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row1", "1.0.3_link": ["Communication.md#2.3.s3.table1.row1"]}, "Should reject GET with \"activityId\" with type 1": {"name": "Should reject GET with \"activityId\" with type 1"}, "Should reject GET with \"activityId\" with type true": {"name": "Should reject GET with \"activityId\" with type true"}, "Should reject GET with \"activityId\" with type [object Object]": {"name": "Should reject GET with \"activityId\" with type [object Object]"}, "Should reject GET with \"activityId\" with type undefined": {"name": "Should reject GET with \"activityId\" with type undefined"}, "An LRS's State Resource rejects a GET request without \"agent\" as a parameter with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a GET request without \"agent\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.4.table1.row2.b", "1.0.2_ref": "7.4.table1.row2.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row2", "1.0.3_link": ["Communication.md#2.3.s3.table1.row2"]}, "An LRS's State Resource rejects a GET request with \"agent\" as a parameter if it is not in JSON format with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a GET request with \"agent\" as a parameter if it is not in JSON format with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row2.a", "1.0.2_ref": "7.4.table1.row2.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row2", "1.0.3_link": ["Communication.md#2.3.s3.table1.row2"]}, "Should reject GET with \"agent\" with type 1": {"name": "Should reject GET with \"agent\" with type 1"}, "Should reject GET with \"agent\" with type true": {"name": "Should reject GET with \"agent\" with type true"}, "Should reject GET with \"agent\" with type not JSON": {"name": "Should reject GET with \"agent\" with type not JSON"}, "Should reject GET with \"agent\" with type undefined": {"name": "Should reject GET with \"agent\" with type undefined"}, "An LRS's State Resource can process a GET request with \"registration\" as a parameter": {"name": "An LRS's State Resource can process a GET request with \"registration\" as a parameter", "1.0.2_ref_text": "multiplicity, 7.4.table1.row3.b", "1.0.2_ref": "7.4.table1.row3.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row3", "1.0.3_link": ["Communication.md#2.3.s3.table1.row3"]}, "An LRS's State Resource rejects a GET request with \"registration\" as a parameter if it is not a UUID with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a GET request with \"registration\" as a parameter if it is not a UUID with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row3.a", "1.0.2_ref": "7.4.table1.row3.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row3", "1.0.3_link": ["Communication.md#2.3.s3.table1.row3"]}, "Should reject GET with \"registration\" with type 1": {"name": "Should reject GET with \"registration\" with type 1"}, "Should reject GET with \"registration\" with type true": {"name": "Should reject GET with \"registration\" with type true"}, "Should reject GET with \"registration\" with type not UUID": {"name": "Should reject GET with \"registration\" with type not UUID"}, "An LRS's State Resource can process a GET request with \"stateId\" as a parameter": {"name": "An LRS's State Resource can process a GET request with \"stateId\" as a parameter", "1.0.2_ref_text": "multiplicity, 7.4.table1.row3.b, 7.4.table2.row3.b", "1.0.2_ref": "7.4.table1.row3.b, 7.4.table2.row3.b, 7.4.table1.row1.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row4", "1.0.3_link": ["Communication.md#2.3.s3.table1.row4"]}, "An LRS's State Resource can process a GET request with \"since\" as a parameter": {"name": "An LRS's State Resource can process a GET request with \"since\" as a parameter", "1.0.2_ref_text": "multiplicity, 7.4.table2.row4.b, 7.4.table2.row3.b", "1.0.2_ref": "7.4.table2.row4.b, 7.4.table2.row3.b", "1.0.3_ref": "multiplicity, Communication 2.3.s4.table1.row4", "1.0.3_link": ["Communication.md#2.3.s4.table1.row4"]}, "An LRS's State Resource rejects a GET request with \"since\" as a parameter if it is not a \"TimeStamp\", with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a GET request with \"since\" as a parameter if it is not a \"TimeStamp\", with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table2.row4.a", "1.0.2_ref": "7.4.table2.row4.a", "1.0.3_ref": "format, Communication 2.3.s4.table1.row4", "1.0.3_link": ["Communication.md#2.3.s4.table1.row4"]}, "An LRS's State Resource upon processing a successful GET request with a valid \"stateId\" as a parameter returns the document satisfying the requirements of the GET and code 200 OK": {"name": "An LRS's State Resource upon processing a successful GET request with a valid \"stateId\" as a parameter returns the document satisfying the requirements of the GET and code 200 OK", "1.0.2_ref_text": "7.4.b", "1.0.2_ref": "7.4.b", "1.0.3_ref": "Communication 2.3.s3", "1.0.3_link": ["Communication.md#2.3.s3"]}, "An LRS's State Resource upon processing a successful GET request without \"stateId\" as a parameter returns an array of ids of state data documents satisfying the requirements of the GET and code 200 OK": {"name": "An LRS's State Resource upon processing a successful GET request without \"stateId\" as a parameter returns an array of ids of state data documents satisfying the requirements of the GET and code 200 OK", "1.0.2_ref_text": "7.4.c", "1.0.2_ref": "7.4.c", "1.0.3_ref": "Communication 2.3.s4", "1.0.3_link": ["Communication.md#2.3.s4"]}, "An LRS's returned array of ids from a successful GET request to the State Resource all refer to documents stored after the TimeStamp in the \"since\" parameter of the GET request": {"name": "An LRS's returned array of ids from a successful GET request to the State Resource all refer to documents stored after the TimeStamp in the \"since\" parameter of the GET request", "1.0.2_ref_text": "7.4.table2.row4", "1.0.2_ref": "7.4.table2.row4", "1.0.3_ref": "Communication 2.3.s4.table1.row4", "1.0.3_link": ["Communication.md#2.3.s4.table1.row4"]}, "An LRS's State Resource accepts DELETE requests": {"name": "An LRS's State Resource accepts DELETE requests", "1.0.2_ref_text": "7.4", "1.0.2_ref": "7.4", "1.0.3_ref": "Communication 2.3", "1.0.3_link": ["Communication.md#2.3"]}, "An LRS's State Resource rejects a DELETE request without \"activityId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a DELETE request without \"activityId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.4.table1.row1.b", "1.0.2_ref": "7.4.table1.row1.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row1", "1.0.3_link": ["Communication.md#2.3.s3.table1.row1"]}, "An LRS's State Resource rejects a DELETE request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a DELETE request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row1.a", "1.0.2_ref": "7.4.table1.row1.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row1", "1.0.3_link": ["Communication.md#2.3.s3.table1.row1"]}, "Should reject DELETE with \"activityId\" with type 1": {"name": "Should reject DELETE with \"activityId\" with type 1"}, "Should reject DELETE with \"activityId\" with type true": {"name": "Should reject DELETE with \"activityId\" with type true"}, "Should reject DELETE with \"activityId\" with type [object Object]": {"name": "Should reject DELETE with \"activityId\" with type [object Object]"}, "Should reject DELETE with \"activityId\" with type undefined": {"name": "Should reject DELETE with \"activityId\" with type undefined"}, "An LRS's State Resource rejects a DELETE request without \"agent\" as a parameter with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a DELETE request without \"agent\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.4.table1.row2.b", "1.0.2_ref": "7.4.table1.row2.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row2", "1.0.3_link": ["Communication.md#2.3.s3.table1.row2"]}, "An LRS's State Resource rejects a DELETE request with \"agent\" as a parameter if it is not in JSON format with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a DELETE request with \"agent\" as a parameter if it is not in JSON format with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row2.a", "1.0.2_ref": "7.4.table1.row2.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row2", "1.0.3_link": ["Communication.md#2.3.s3.table1.row2"]}, "Should reject DELETE with \"agent\" with type 1": {"name": "Should reject DELETE with \"agent\" with type 1"}, "Should reject DELETE with \"agent\" with type true": {"name": "Should reject DELETE with \"agent\" with type true"}, "Should reject DELETE with \"agent\" with type not JSON": {"name": "Should reject DELETE with \"agent\" with type not JSON"}, "Should reject DELETE with \"agent\" with type undefined": {"name": "Should reject DELETE with \"agent\" with type undefined"}, "An LRS's State Resource can process a DELETE request with \"registration\" as a parameter": {"name": "An LRS's State Resource can process a DELETE request with \"registration\" as a parameter", "1.0.2_ref_text": "multiplicity, 7.4.table1.row3.b", "1.0.2_ref": "7.4.table1.row3.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row3", "1.0.3_link": ["Communication.md#2.3.s3.table1.row3"]}, "An LRS's State Resource rejects a DELETE request with \"registration\" as a parameter if it is not a UUID with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a DELETE request with \"registration\" as a parameter if it is not a UUID with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table1.row3.a", "1.0.2_ref": "7.4.table1.row3.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row3", "1.0.3_link": ["Communication.md#2.3.s3.table1.row3"]}, "Should reject DELETE with \"registration\" with type 1": {"name": "Should reject DELETE with \"registration\" with type 1"}, "Should reject DELETE with \"registration\" with type true": {"name": "Should reject DELETE with \"registration\" with type true"}, "Should reject DELETE with \"registration\" with type not UUID": {"name": "Should reject DELETE with \"registration\" with type not UUID"}, "An LRS's State Resource can process a DELETE request with \"stateId\" as a parameter": {"name": "An LRS's State Resource can process a DELETE request with \"stateId\" as a parameter", "1.0.2_ref_text": "multiplicity, 7.4.table1.row3.b, 7.4.table2.row3.b", "1.0.2_ref": "7.4.table1.row3.b, 7.4.table2.row3.b, 7.4.table1.row1.b", "1.0.3_ref": "multiplicity, Communication 2.3.s3.table1.row4", "1.0.3_link": ["Communication.md#2.3.s3.table1.row4"]}, "An LRS's State Resource rejects a DELETE request with \"since\" as a parameter if it is not a \"TimeStamp\", with error code 400 Bad Request": {"name": "An LRS's State Resource rejects a DELETE request with \"since\" as a parameter if it is not a \"TimeStamp\", with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.4.table2.row4.a", "1.0.2_ref": "7.4.table3.row4.a", "1.0.3_ref": "format, Communication 2.3.s3.table1.row4", "1.0.3_link": ["Communication.md#2.3.s3.table1.row4"]}, "An LRS's State Resource upon processing a successful DELETE request with a valid \"stateId\" as a parameter deletes the document satisfying the requirements of the DELETE and returns code 204 No Content": {"name": "An LRS's State Resource upon processing a successful DELETE request with a valid \"stateId\" as a parameter deletes the document satisfying the requirements of the DELETE and returns code 204 No Content", "1.0.2_ref_text": "7.4.b", "1.0.2_ref": "7.4.b", "1.0.3_ref": "Communication 2.3.s3", "1.0.3_link": ["Communication.md#2.3.s3"]}, "An LRS's State Resource upon processing a successful DELETE request without \"stateId\" as a parameter deletes documents satisfying the requirements of the DELETE and code 204 No Content": {"name": "An LRS's State Resource upon processing a successful DELETE request without \"stateId\" as a parameter deletes documents satisfying the requirements of the DELETE and code 204 No Content", "1.0.2_ref_text": "7.4.d", "1.0.2_ref": "7.4.d", "1.0.3_ref": "Communication 2.3.s5", "1.0.3_link": ["Communication.md#2.3.s5"]}, "An LRS's Activities Resource accepts GET requests": {"name": "An LRS's Activities Resource accepts GET requests", "1.0.2_ref_text": "7.5", "1.0.2_ref": "7.5", "1.0.3_ref": "Communication 2.5", "1.0.3_link": ["Communication.md#2.5"]}, "An LRS's Activities Resource rejects a GET request without \"activityId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Activities Resource rejects a GET request without \"activityId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.5.table1.row1.b", "1.0.2_ref": "7.5.table1.row1.b", "1.0.3_ref": "multiplicity, Communication 2.5.s1.table1.row1", "1.0.3_link": ["Communication.md#2.5.s1.table1.row1"]}, "An LRS's Activities Resource rejects a GET request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's Activities Resource rejects a GET request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.5.table1.row1.a", "1.0.2_ref": "7.5.table1.row1.a", "1.0.3_ref": "format, Communication 2.5.s1.table1.row1", "1.0.3_link": ["Communication.md#2.5.s1.table1.row1"]}, "An LRS's Activities Resource upon processing a successful GET request returns the complete Activity Object": {"name": "An LRS's Activities Resource upon processing a successful GET request returns the complete Activity Object", "1.0.2_ref_text": "7.5", "1.0.2_ref": "7.5", "1.0.3_ref": "Communication 2.5.s1", "1.0.3_link": ["Communication.md#2.5.s1"]}, "An LRS's Activity Profile Resource accepts PUT requests": {"name": "An LRS's Activity Profile Resource accepts PUT requests", "1.0.2_ref_text": "7.5", "1.0.2_ref": "7.5", "1.0.3_ref": "Communication 2.7", "1.0.3_link": ["Communication.md#2.7"]}, "An LRS's Activity Profile Resource rejects a PUT request without \"activityId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a PUT request without \"activityId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.5.table2.row1.c", "1.0.2_ref": "7.5.table2.row1.c", "1.0.3_ref": "multiplicity, Communication 2.7.s3.table1.row1", "1.0.3_link": ["Communication.md#2.7.s3.table1.row1"]}, "An LRS's Activity Profile Resource rejects a PUT request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a PUT request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.5.table2.row2.a", "1.0.2_ref": "7.5.table2.row2.a", "1.0.3_ref": "format, Communication 2.7.s3.table1.row1", "1.0.3_link": ["Communication.md#2.7.s3.table1.row1"]}, "Should reject PUT with \"activityId\" with type 1": {"name": "Should reject PUT with \"activityId\" with type 1"}, "Should reject PUT with \"activityId\" with type true": {"name": "Should reject PUT with \"activityId\" with type true"}, "Should reject PUT with \"activityId\" with type [object Object]": {"name": "Should reject PUT with \"activityId\" with type [object Object]"}, "An LRS's Activity Profile Resource rejects a PUT request without \"profileId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a PUT request without \"profileId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.5.table2.row1.c", "1.0.2_ref": "7.5.table2.row2.c", "1.0.3_ref": "multiplicity, Communication 2.7.s3.table1.row2", "1.0.3_link": ["Communication.md#2.7.s3.table1.row2"]}, "An LRS's Activity Profile Resource upon processing a successful PUT request returns code 204 No Content": {"name": "An LRS's Activity Profile Resource upon processing a successful PUT request returns code 204 No Content", "1.0.2_ref_text": "7.5.b", "1.0.2_ref": "7.5.b", "1.0.3_ref": "Communication 2.7.s2", "1.0.3_link": ["Communication.md#2.7.s2"]}, "An LRS's Activity Profile Resource accepts POST requests": {"name": "An LRS's Activity Profile Resource accepts POST requests", "1.0.2_ref_text": "7.5", "1.0.2_ref": "7.5", "1.0.3_ref": "Communication 2.7", "1.0.3_link": ["Communication.md#2.7"]}, "An LRS's Activity Profile Resource rejects a POST request without \"activityId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a POST request without \"activityId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.5.table2.row1.c", "1.0.2_ref": "7.5.table2.row1.c", "1.0.3_ref": "multiplicity, Communication 2.7.s3.table1.row1", "1.0.3_link": ["Communication.md#2.7.s3.table1.row1"]}, "An LRS's Activity Profile Resource rejects a POST request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a POST request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.5.table2.row2.a", "1.0.2_ref": "7.5.table2.row2.a", "1.0.3_ref": "format, Communication 2.7.s3.table1.row1", "1.0.3_link": ["Communication.md#2.7.s3.table1.row1"]}, "Should reject POST with \"activityId\" with type 1": {"name": "Should reject POST with \"activityId\" with type 1"}, "Should reject POST with \"activityId\" with type true": {"name": "Should reject POST with \"activityId\" with type true"}, "Should reject POST with \"activityId\" with type [object Object]": {"name": "Should reject POST with \"activityId\" with type [object Object]"}, "An LRS's Activity Profile Resource rejects a POST request without \"profileId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a POST request without \"profileId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.5.table2.row1.c", "1.0.2_ref": "7.5.table2.row1.c", "1.0.3_ref": "multiplicity, Communication 2.7.s3.table1.row2", "1.0.3_link": ["Communication.md#2.7.s3.table1.row2"]}, "An LRS's Activity Profile Resource upon processing a successful POST request returns code 204 No Content": {"name": "An LRS's Activity Profile Resource upon processing a successful POST request returns code 204 No Content", "1.0.2_ref_text": "7.5.b", "1.0.2_ref": "7.5.b", "1.0.3_ref": "Communication 2.7.s2", "1.0.3_link": ["Communication.md#2.7.s2"]}, "An LRS's Activity Profile Resource accepts DELETE requests": {"name": "An LRS's Activity Profile Resource accepts DELETE requests", "1.0.2_ref_text": "7.5", "1.0.2_ref": "7.5", "1.0.3_ref": "Communication 2.7", "1.0.3_link": ["Communication.md#2.7"]}, "An LRS's Activity Profile Resource rejects a DELETE request without \"activityId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a DELETE request without \"activityId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.5.table2.row1.c", "1.0.2_ref": "7.5.table2.row1.c", "1.0.3_ref": "multiplicity, Communication 2.7.s3.table1.row1", "1.0.3_link": ["Communication.md#2.7.s3.table1.row1"]}, "An LRS's Activity Profile Resource rejects a DELETE request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a DELETE request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.5.table2.row2.a", "1.0.2_ref": "7.5.table2.row2.a", "1.0.3_ref": "format, Communication 2.7.s3.table1.row1", "1.0.3_link": ["Communication.md#2.7.s3.table1.row1"]}, "An LRS's Activity Profile Resource rejects a PUT request without \"profileId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a PUT request without \"profileId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.3_ref": "format, Communication 2.7.s3.table1.row2", "1.0.3_link": ["Communication.md#2.7.s3.table1.row2"]}, "An LRS's Activity Profile Resource rejects a POST request without \"profileId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a POST request without \"profileId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.3_ref": "format, Communication 2.7.s3.table1.row2", "1.0.3_link": ["Communication.md#2.7.s3.table1.row2"]}, "An LRS's Activity Profile Resource rejects a GET request without \"profileId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a GET request without \"profileId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.3_ref": "format, Communication 2.7.s3.table1.row2", "1.0.3_link": ["Communication.md#2.7.s3.table1.row2"]}, "An LRS's Activity Profile Resource rejects a DELETE request without \"profileId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a DELETE request without \"profileId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.5.table2.row1.c", "1.0.2_ref": "7.5.table2.row1.c", "1.0.3_ref": "multiplicity, Communication 2.7.s3.table1.row2", "1.0.3_link": ["Communication.md#2.7.s3.table1.row2"]}, "An LRS's Activity Profile Resource upon processing a successful DELETE request deletes the associated profile and returns code 204 No Content": {"name": "An LRS's Activity Profile Resource upon processing a successful DELETE request deletes the associated profile and returns code 204 No Content", "1.0.2_ref_text": "7.5.b", "1.0.2_ref": "7.5.b", "1.0.3_ref": "Communication 2.7.s2", "1.0.3_link": ["Communication.md#2.7.s2"]}, "An LRS's Activity Profile Resource accepts GET requests": {"name": "An LRS's Activity Profile Resource accepts GET requests", "1.0.2_ref_text": "7.5", "1.0.2_ref": "7.5", "1.0.3_ref": "Communication 2.7", "1.0.3_link": ["Communication.md#2.7"]}, "An LRS's Activity Profile Resource rejects a GET request without \"activityId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a GET request without \"activityId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.5.table2.row1.c", "1.0.2_ref": "7.5.table2.row1.c", "1.0.3_ref": "multiplicity, Communication 2.7.s3.table1.row1, Communication 2.7.s4.table1.row1", "1.0.3_link": ["Communication.md#2.7.s3.table1.row1", "Communication.md#2.7.s4.table1.row1"]}, "An LRS's Activity Profile Resource rejects a GET request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a GET request with \"activityId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.5.table2.row1.a", "1.0.2_ref": "7.5.table2.row2.a", "1.0.3_ref": "format, Communication 2.7.s3.table1.row1, Communication 2.7.s3.table1.row1", "1.0.3_link": ["Communication.md#2.7.s3.table1.row1", "Communication.md#2.7.s4.table1.row1"]}, "An LRS's Activity Profile Resource can process a GET request with \"since\" as a parameter": {"name": "An LRS's Activity Profile Resource can process a GET request with \"since\" as a parameter", "1.0.2_ref_text": "multiplicity, 7.5.table3.row2.c, 7.5.table3.row2.b", "1.0.2_ref": "7.5.table3.row2.c, 7.5.table3.row2.b", "1.0.3_ref": "format, Communication 2.7.s4.table1.row2", "1.0.3_link": ["Communication.md#2.7.s4.table1.row2"]}, "An LRS's Activity Profile Resource rejects a GET request with \"since\" as a parameter if it is not a \"TimeStamp\", with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a GET request with \"since\" as a parameter if it is not a \"TimeStamp\", with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.5.table3.row2.a", "1.0.2_ref": "7.5.table3.row2.a", "1.0.3_ref": "format, Communication 2.7.s4.table1.row2", "1.0.3_link": ["Communication.md#2.7.s4.table1.row2"]}, "Should reject GET with \"since\" with type": {"name": "Should reject GET with \"since\" with type"}, "An LRS's Activity Profile Resource upon processing a successful GET request with a valid \"profileId\" as a parameter returns the document satisfying the requirements of the GET and code 200 OK": {"name": "An LRS's Activity Profile Resource upon processing a successful GET request with a valid \"profileId\" as a parameter returns the document satisfying the requirements of the GET and code 200 OK", "1.0.2_ref_text": "7.5.c", "1.0.2_ref": "7.5.c", "1.0.3_ref": "Communication 2.7.s3", "1.0.3_link": ["Communication.md#2.7.s3"]}, "An LRS's Activity Profile Resource upon processing a successful GET request without \"profileId\" as a parameter returns an array of ids of activity profile documents satisfying the requirements of the GET and code 200 OK": {"name": "An LRS's Activity Profile Resource upon processing a successful GET request without \"profileId\" as a parameter returns an array of ids of activity profile documents satisfying the requirements of the GET and code 200 OK", "1.0.2_ref_text": "7.5.d", "1.0.2_ref": "7.5.d", "1.0.3_ref": "Communication 2.7.s4", "1.0.3_link": ["Communication.md#2.7.s4"]}, "An LRS's returned array of ids from a successful GET request to the Activity Profile Resource all refer to documents stored after the TimeStamp in the \"since\" parameter of the GET request if such a parameter was present": {"name": "An LRS's returned array of ids from a successful GET request Activity Profile Resource all refer to documents stored after the TimeStamp in the \"since\" parameter of the GET request if such a parameter was present", "1.0.2_ref_text": "7.6.table4.row2, 7.6.g", "1.0.2_ref": "7.6.table4.row2, 7.6.g", "1.0.3_ref": "Communication 2.7.s4.table1.row2", "1.0.3_link": ["Communication.md#2.7.s4.table1.row2"]}, "An LRS's returned array of ids from a successful GET request to the Agent Profile Resource all refer to documents stored after the TimeStamp in the \"since\" parameter of the GET request if such a parameter was present": {"name": "An LRS's returned array of ids from a successful GET request Agent Profile Resource all refer to documents stored after the TimeStamp in the \"since\" parameter of the GET request if such a parameter was present", "1.0.2_ref_text": "7.6.table4.row2, 7.6.g", "1.0.2_ref": "7.6.table4.row2, 7.6.g", "1.0.3_ref": "Communication 2.6.s4.table1.row2", "1.0.3_link": ["Communication.md#2.6.s4.table1.row2"]}, "An LRS's Agents Resource accepts GET requests": {"name": "An LRS's Agents Resource accepts GET requests", "1.0.2_ref_text": "7.6", "1.0.2_ref": "7.6", "1.0.3_ref": "Communication 2.4.s2", "1.0.3_link": ["Communication.md#2.4.s2"]}, "An LRS's Agents Resource rejects a GET request without \"agent\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Agents Resource rejects a GET request without \"agent\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.6.table2.row1.c", "1.0.2_ref": "7.6.table2.row1.c", "1.0.3_ref": "Communication 2.4.s2.table1.row1", "1.0.3_link": ["Communication.md#2.4.s2.table1.row1"]}, "An LRS's Agent Profile Resource accepts PUT requests": {"name": "An LRS's Agent Profile Resource accepts PUT requests", "1.0.2_ref_text": "7.6", "1.0.2_ref": "7.6", "1.0.3_ref": "Communication 2.6.s2", "1.0.3_link": ["Communication.md#2.6.s2"]}, "An LRS's Agent Profile Resource rejects a PUT request without \"agent\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a PUT request without \"agent\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.6.table3.row1.c", "1.0.2_ref": "7.6.table3.row1.c", "1.0.3_ref": "Communication 2.6.s3.table1.row1", "1.0.3_link": ["Communication.md#2.6.s3.table1.row1"]}, "An LRS's Agent Profile Resource rejects a PUT request with \"agent\" as a parameter if it is not an Agent Object with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a PUT request with \"agent\" as a parameter if it is not an Agent Object with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.6.table3.row1.a", "1.0.2_ref": "7.6.table3.row1.a", "1.0.3_ref": "Communication 2.6.s3.table1.row1", "1.0.3_link": ["Communication.md#2.6.s3.table1.row1"]}, "Should reject PUT with \"agent\" with type 1": {"name": "Should reject PUT with \"agent\" with type 1"}, "Should reject PUT with \"agent\" with type true": {"name": "Should reject PUT with \"agent\" with type true"}, "Should reject PUT with \"agent\" with type not Agent": {"name": "Should reject PUT with \"agent\" with type not Agent"}, "Should reject PUT with \"agent\" with type [object Object]": {"name": "Should reject PUT with \"agent\" with type [object Object]"}, "An LRS's Agent Profile Resource rejects a PUT request without \"profileId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a PUT request without \"profileId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.6.table3.row2.c", "1.0.2_ref": "7.6.table3.row2.c", "1.0.3_ref": "Communication 2.6.s3.table1.row2", "1.0.3_link": ["Communication.md#2.6.s3.table1.row2"]}, "An LRS's Agent Profile Resource upon processing a successful PUT request returns code 204 No Content": {"name": "An LRS's Agent Profile Resource upon processing a successful PUT request returns code 204 No Content", "1.0.2_ref_text": "7.6.e", "1.0.2_ref": "7.6.e", "1.0.3_ref": "Communication 2.6.s3, XAPI-00273", "1.0.3_link": ["Communication.md#2.6.s3"]}, "An LRS's Agent Profile Resource upon processing a PUT request without an ETag header returns an error code and message": {"name": "An LRS's Agent Profile Resource upon processing a PUT request without an ETag header returns an error code and message", "1.0.2_ref_text": "7.6.e", "1.0.2_ref": "7.6.e", "1.0.3_ref": "Communication 2.6.s3, XAPI-00273", "1.0.3_link": ["Communication.md#2.6.s3"]}, "An LRS's Agent Profile Resource accepts POST requests": {"name": "An LRS's Agent Profile Resource accepts POST requests", "1.0.2_ref_text": "7.6", "1.0.2_ref": "7.6", "1.0.3_ref": "Communication 2.6.s2", "1.0.3_link": ["Communication.md#2.6.s2"]}, "An LRS's Agent Profile Resource rejects a POST request without \"agent\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a POST request without \"agent\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.6.table3.row1.c", "1.0.2_ref": "7.6.table3.row1.c", "1.0.3_ref": "Communication 2.6.s3.table1.row1", "1.0.3_link": ["Communication.md#2.6.s3.table1.row1"]}, "An LRS's Agent Profile Resource rejects a POST request with \"agent\" as a parameter if it is not an Agent Object with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a POST request with \"agent\" as a parameter if it is not an Agent Object with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.6.table3.row1.a", "1.0.2_ref": "7.6.table3.row1.a", "1.0.3_ref": "Communication 2.6.s3.table1.row1", "1.0.3_link": ["Communication.md#2.6.s3.table1.row1"]}, "Should reject POST with \"agent\" with type 1": {"name": "Should reject POST with \"agent\" with type 1"}, "Should reject POST with \"agent\" with type true": {"name": "Should reject POST with \"agent\" with type true"}, "Should reject POST with \"agent\" with type [object Object]": {"name": "Should reject POST with \"agent\" with type [object Object]"}, "An LRS's Agent Profile Resource rejects a POST request without \"profileId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a POST request without \"profileId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.6.table3.row2.c", "1.0.2_ref": "7.6.table3.row2.c", "1.0.3_ref": "Communication 2.6.s3.table1.row2", "1.0.3_link": ["Communication.md#2.6.s3.table1.row2"]}, "An LRS's Agent Profile Resource upon processing a successful POST request returns code 204 No Content": {"name": "An LRS's Agent Profile Resource upon processing a successful POST request returns code 204 No Content", "1.0.2_ref_text": "7.6.e", "1.0.2_ref": "7.6.e", "1.0.3_ref": "Communication 2.6.s3", "1.0.3_link": ["Communication.md#2.6.s3"]}, "An LRS's Agent Profile Resource accepts DELETE requests": {"name": "An LRS's Agent Profile Resource accepts DELETE requests", "1.0.2_ref_text": "7.6", "1.0.2_ref": "7.6", "1.0.3_ref": "Communication 2.6.s2", "1.0.3_link": ["Communication.md#2.6.s2"]}, "An LRS's Agent Profile Resource rejects a DELETE request without \"agent\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a DELETE request without \"agent\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.6.table3.row1.c", "1.0.2_ref": "7.6.table3.row1.c", "1.0.3_ref": "Communication 2.6.s3.table1.row1", "1.0.3_link": ["Communication.md#2.6.s3.table1.row1"]}, "An LRS's Agent Profile Resource rejects a DELETE request with \"agent\" as a parameter if it is not an Agent Object with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a DELETE request with \"agent\" as a parameter if it is not an Agent Object with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.6.table3.row1.a", "1.0.2_ref": "7.6.table3.row1.a", "1.0.3_ref": "Communication 2.6.s3.table1.row1", "1.0.3_link": ["Communication.md#2.6.s3.table1.row1"]}, "Should reject DELETE with \"agent\" with type [object Object]": {"name": "Should reject DELETE with \"agent\" with type [object Object]"}, "An LRS's Agent Profile Resource rejects a DELETE request without \"profileId\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a DELETE request without \"profileId\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.6.table3.row2.c", "1.0.2_ref": "7.6.table3.row2.c", "1.0.3_ref": "Communication 2.6.s3.table1.row2", "1.0.3_link": ["Communication.md#2.6.s3.table1.row2"]}, "An LRS's Agent Profile Resource rejects a DELETE request with \"profileId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a DELETE request with \"profileId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.6.table3.row2.a", "1.0.2_ref": "7.6.table3.row2.a", "1.0.3_ref": "Communication 2.6.s3.table1.row2", "1.0.3_link": ["Communication.md#2.6.s3.table1.row2"]}, "Should reject DELETE with \"profileId\" with type 1": {"name": "Should reject DELETE with \"profileId\" with type 1"}, "Should reject DELETE with \"profileId\" with type true": {"name": "Should reject DELETE with \"profileId\" with type true"}, "Should reject DELETE with \"profileId\" with type [object Object]": {"name": "Should reject DELETE with \"profileId\" with type [object Object]"}, "An LRS's Agent Profile Resource upon processing a successful DELETE request deletes the associated profile and returns code 204 No Content": {"name": "An LRS's Agent Profile Resource upon processing a successful DELETE request deletes the associated profile and returns code 204 No Content", "1.0.2_ref_text": "7.6.e", "1.0.2_ref": "7.6.e", "1.0.3_ref": "Communication 2.6.s3", "1.0.3_link": ["Communication.md#2.6.s3"]}, "An LRS's Agent Profile Resource accepts GET requests": {"name": "An LRS's Agent Profile Resource accepts GET requests", "1.0.2_ref_text": "7.6", "1.0.2_ref": "7.6", "1.0.3_ref": "Communication 2.6.s2", "1.0.3_link": ["Communication.md#2.6.s2"]}, "An LRS's Agent Profile Resource rejects a GET request without \"agent\" as a parameter with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a GET request without \"agent\" as a parameter with error code 400 Bad Request", "1.0.2_ref_text": "multiplicity, 7.6.table3.row1.c, 7.6.table4.row1.c", "1.0.2_ref": "7.6.table3.row1.c, 7.6.table4.row1.c", "1.0.3_ref": "Communication 2.6.s4.table1.row1", "1.0.3_link": ["Communication.md#2.6.s4.table1.row1"]}, "An LRS's Agent Profile Resource rejects a GET request with \"agent\" as a parameter if it is not an Actor Object with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a GET request with \"agent\" as a parameter if it is not an Actor Object with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.6.table3.row1.c, 7.6.table4.row1.c", "1.0.2_ref": "7.6.table3.row1.c, 7.6.table4.row1.c", "1.0.3_ref": "Communication 2.6.s4.table1.row1", "1.0.3_link": ["Communication.md#2.6.s4.table1.row1"]}, "Should reject GET with \"agent\" with type [object Object]": {"name": "Should reject GET with \"agent\" with type [object Object]"}, "Should reject GET with \"agent\" with type not Actor": {"name": "Should reject GET with \"agent\" with type not Actor"}, "An LRS's Agent Profile Resource can process a GET request with \"since\" as a parameter": {"name": "An LRS's Agent Profile Resource can process a GET request with \"since\" as a parameter", "1.0.2_ref_text": "Multiplicity, 7.6.table4.row2.a, 7.5.table4.row2.c", "1.0.2_ref": "7.6.table4.row2.a", "1.0.3_ref": "Communication 2.6.s4.table1.row2", "1.0.3_link": ["Communication.md#2.6.s4.table1.row2"]}, "An LRS's Agent Profile Resource rejects a GET request with \"since\" as a parameter if it is not a \"TimeStamp\", with error code 400 Bad Request": {"name": "An LRS's Agent Profile Resource rejects a GET request with \"since\" as a parameter if it is not a \"TimeStamp\", with error code 400 Bad Request", "1.0.2_ref_text": "format, 7.6.table4.row2.a", "1.0.2_ref": "7.6.table4.row2.a", "1.0.3_ref": "Communication 2.6.s4.table1.row2", "1.0.3_link": ["Communication.md#2.6.s4.table1.row2"]}, "Should reject GET with \"since\" with type 1": {"name": "Should reject GET with \"since\" with type 1"}, "Should reject GET with \"since\" with type true": {"name": "Should reject GET with \"since\" with type true"}, "Should reject GET with \"since\" with type [object Object]": {"name": "Should reject GET with \"since\" with type [object Object]"}, "Should reject GET with \"since\" with type not timestamp": {"name": "Should reject GET with \"since\" with type not timestamp"}, "An LRS's Agent Profile Resource upon processing a successful GET request with a valid \"profileId\" as a parameter returns the document satisfying the requirements of the GET and code 200 OK": {"name": "An LRS's Agent Profile Resource upon processing a successful GET request with a valid \"profileId\" as a parameter returns the document satisfying the requirements of the GET and code 200 OK", "1.0.2_ref_text": "7.6, 7.6.f", "1.0.2_ref": "7.6, 7.6.f", "1.0.3_ref": "Communication 2.6.s3", "1.0.3_link": ["Communication.md#2.6.s3"]}, "An LRS's Agent Profile Resource upon processing a successful GET request without \"profileId\" as a parameter returns an array of ids of agent profile documents satisfying the requirements of the GET and code 200 OK": {"name": "An LRS's Agent Profile Resource upon processing a successful GET request without \"profileId\" as a parameter returns an array of ids of agent profile documents satisfying the requirements of the GET and code 200 OK", "1.0.2_ref_text": "7.6, 7.6.g", "1.0.2_ref": "7.6, 7.6.g", "1.0.3_ref": "Communication 2.6.s4", "1.0.3_link": ["Communication.md#2.6.s4"]}, "An LRS's About Resource accepts GET requests": {"name": "An LRS's About Resource accepts GET requests", "1.0.2_ref_text": "7.7.b", "1.0.2_ref": "7.7.b", "1.0.3_ref": "Communication 2.8", "1.0.3_link": ["Communication.md#2.8"]}, "An LRS's About Resource upon processing a successful GET request returns a version property and code 200 OK": {"name": "An LRS's About Resource upon processing a successful GET request returns a version property and code 200 OK", "1.0.2_ref_text": "multiplicity, 7.7.table1.row1.c, 7.7.c", "1.0.2_ref": "7.7.table1.row1.c, 7.7.c", "1.0.3_ref": "Communication 2.8.s4", "1.0.3_link": ["Communication.md#2.8.s4"]}, "An LRS's About Resource's version property is an array of strings": {"name": "An LRS's About Resource's version property is an array of strings", "1.0.2_ref_text": "format, 7.7.table1.row1.a", "1.0.2_ref": "7.7.table1.row1.a", "1.0.3_ref": "Communication 2.8.s4.table1.row1", "1.0.3_link": ["Communication.md#2.8.s4.table1.row1"]}, "An LRS's About Resource's version property contains at least one string of \"1.0.3\"": {"name": "An LRS's About Resource's version property contains at least one string of \"1.0.3\"", "1.0.2_ref_text": "7.7.d", "1.0.2_ref": "7.7.d", "1.0.3_ref": "Communication 2.8.s5.b1.b1", "1.0.3_link": ["Communication.md#2.8.s5.b1.b1"]}, "An LRS's About Resource's version property can only have values of \"0.9\", \"0.95\", \"1.0.0\", or \"\"1.0.\" + X\" with": {"name": "An LRS's About Resource's version property can only have values of \".9\", \".95\", \"1.0.0\", or \"\"1.0.\" + X\" with", "1.0.2_ref_text": "7.7.d.a", "1.0.2_ref": "7.7.d.a", "1.0.3_ref": "Communication 2.8.s5.b1.b1", "1.0.3_link": ["Communication.md#2.8.s5.b1.b1"]}, "An LRS's About Resource upon processing a successful GET request can return an Extension with code 200 OK": {"name": "An LRS's About Resource upon processing a successful GET request can return an Extension with code 200 OK", "1.0.2_ref_text": "multiplicity, 7.7.table1.row2.c, 7.7.c", "1.0.2_ref": "7.7.table1.row2.c, 7.7.c", "1.0.3_ref": "Communication 2.8.s4.table1.row2", "1.0.3_link": ["Communication.md#2.8.s4.table1.row2"]}, "Any LRS Resource that accepts a POST request can accept a POST request with a single query string parameter named \"method\" on that request": {"name": "Any LRS Resource that accepts a POST request can accept a POST request with a single query string parameter named \"method\" on that request", "1.0.2_ref_text": "7.8.a", "1.0.2_ref": "7.8.a", "1.0.3_ref": "Communication 1.3.s3.b2", "1.0.3_link": ["Communication.md#1.3.s3.b2"]}, "An LRS must parse the body of a Cross Origin Request and construct a new Request from it with the type of Request being the same as the value of the \"method\" parameter": {"name": "An LRS must parse the body of a Cross Origin Request and construct a new Request from it with the type of Request being the same as the value of the \"method\" parameter", "1.0.2_ref_text": "7.8.a, 7.8.b", "1.0.2_ref": "7.8.a, 7.8.b", "1.0.3_ref": "Communication 1.3.s3.b6", "1.0.3_link": ["Communication.md#1.3.s3.b6"]}, "An LRS will map form parameters from the body of the Cross Origin Request to the similarly named HTTP Headers in the new Request": {"name": "An LRS will map form parameters from the body of the Cross Origin Request to the similarly named HTTP Headers in the new Request", "1.0.2_ref_text": "7.8.b", "1.0.2_ref": "7.8.b", "1.0.3_ref": "Communication 1.3.s3.b7", "1.0.3_link": ["Communication.md#1.3.s3.b7"]}, "An LRS rejects a new Request in the same way for violating rules of this document as it would a normal Request": {"name": "An LRS rejects a new Request in the same way for violating rules of this document as it would a normal Request **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 1.3, Implicit", "1.0.3_link": ["Communication.md#1.3"]}, "An LRS will reject any request sending content which does not have a form parameter with the name of \"content\"": {"name": "An LRS will reject any request sending content which does not have a form parameter with the name of \"content\"", "1.0.2_ref_text": "7.8.c", "1.0.2_ref": "7.8.c", "1.0.3_ref": "Communication 1.3.s3.b4", "1.0.3_link": ["Communication.md#1.3.s3.b4"]}, "An LRS will treat the content of the form parameter named \"content\" as a UTF-8 String": {"name": "An LRS will treat the content of the form parameter named \"content\" as a UTF-8 String", "1.0.3_ref": "Communication 1.3.s3.b4, Communication 1.3.s3.b5", "1.0.3_link": ["Communication.md#1.3.s3.b4", "Communication.md#1.3.s3.b5"]}, "An LRS will reject a Cross Origin Request or new Request which contains any extra information with error code 400 Bad Request": {"name": "An LRS will reject a Cross Origin Request or new Request which contains any extra information with error code 400 Bad Request", "1.0.3_ref": "Communication 1.3.s3.b4", "1.0.3_link": ["Communication.md#1.3.s3.b4"]}, "An LRS will reject a new Request with a form parameter named \"content\" if \"content\" is found to be binary data with error code 400 Bad Request": {"name": "An LRS will reject a new Request with a form parameter named \"content\" if \"content\" is found to be binary data with error code 400 Bad Request", "1.0.2_ref_text": "7.8.c", "1.0.2_ref": "7.8.c", "1.0.3_ref": "Communication 1.3.s3.b5", "1.0.3_link": ["Communication.md#1.3.s3.b5"]}, "An LRS will reject a Cross Origin Request which attempts to send attachment data with error code 400 Bad Request": {"name": "An LRS will reject a Cross Origin Request which attempts to send attachment data with error code 400 Bad Request", "1.0.2_ref_text": "7.8.d", "1.0.2_ref": "7.8.d", "1.0.3_ref": "Communication 1.3.s3.b14", "1.0.3_link": ["Communication.md#1.3.s3.b14"]}, "An LRS accepts HEAD requests": {"name": "An LRS accepts HEAD requests", "1.0.2_ref_text": "7.10.a", "1.0.2_ref": "7.10.a", "1.0.3_ref": "Communication 1.1", "1.0.3_link": ["Communication.md#1.1"]}, "An LRS accepts HEAD requests without Content-Length headers": {"name": "An LRS accepts HEAD requests without Content-Length headers", "1.0.2_ref_text": "7.10.a", "1.0.2_ref": "7.10.a", "1.0.3_ref": "Communication 1.1", "1.0.3_link": ["Communication.md#1.1"]}, "An LRS accepts GET requests without Content-Length headers": {"name": "An LRS accepts GET requests without Content-Length headers", "1.0.2_ref_text": "7.10.a", "1.0.2_ref": "7.10.a", "1.0.3_ref": "Communication 1.1", "1.0.3_link": ["Communication.md#1.1"]}, "should succeed GET about with no body": {"name": "should succeed GET about with no body"}, "should succeed GET activities with no body": {"name": "should succeed GET activities with no body"}, "should succeed GET activities profile with no body": {"name": "should succeed GET activities profile with no body"}, "should succeed GET activities state with no body": {"name": "should succeed GET activities state with no body"}, "should succeed GET agents with no body": {"name": "should succeed GET agents with no body"}, "should succeed GET agents profile with no body": {"name": "should succeed GET agents profile with no body"}, "should succeed GET statements with no body": {"name": "should succeed GET statements with no body"}, "An LRS responds to a HEAD request in the same way as a GET request, but without the message-body": {"name": "An LRS responds to a HEAD request in the same way as a GET request, but without the message-body", "1.0.2_ref_text": "7.10.a, 7.10.a.a", "1.0.2_ref": "7.10.a, 7.10.a.a", "1.0.3_ref": "Communication 1.1.s3.b1", "1.0.3_link": ["Communication.md#1.1.s3.b1"]}, "should succeed HEAD about with no body": {"name": "should succeed HEAD about with no body"}, "should succeed HEAD activities with no body": {"name": "should succeed HEAD activities with no body"}, "should succeed HEAD activities profile with no body": {"name": "should succeed HEAD activities profile with no body"}, "should succeed HEAD activities state with no body": {"name": "should succeed HEAD activities state with no body"}, "should succeed HEAD agents with no body": {"name": "should succeed HEAD agents with no body"}, "should succeed HEAD agents profile with no body": {"name": "should succeed HEAD agents profile with no body"}, "should succeed HEAD statements with no body": {"name": "should succeed HEAD statements with no body"}, "A Person Object is an Object": {"name": "A Person Object is an Object", "1.0.2_ref_text": "7.6", "1.0.2_ref": "7.6", "1.0.3_ref": "Communication 2.4.s2", "1.0.3_link": ["Communication.md#2.4.s2"]}, "An LRS's Agent Resource upon processing a successful GET request returns a Person Object if the \"agent\" parameter can be found in the LRS and code 200 OK": {"name": "An LRS's Agent Resource upon processing a successful GET request returns a Person Object if the \"agent\" parameter can be found in the LRS and code 200 OK", "1.0.3_ref": "Communication 2.4.s2.table1.row1", "1.0.3_link": ["Communication.md#2.4.s2.table1.row1"]}, "A Person Object's \"objectType\" property is a String and is \"Person\"": {"name": "A Person Object's \"objectType\" property is a String and is \"Person\"", "1.0.2_ref_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, 7.6.table1.row1.a, 7.6.table1.row1.b", "1.0.2_ref": "7.6.table1.row1.a, 7.6.table1.row1.b", "1.0.3_ref": "Communication 2.4.s5.table1.row1", "1.0.3_link": ["Communication.md#2.4.s5.table1.row1"]}, "A Person Object's \"name\" property is an Array of Strings": {"name": "A Person Object's \"name\" property is an Array of Strings", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row2.a", "1.0.2_ref": "7.6.table1.row2.a", "1.0.3_ref": "Communication 2.4.s5.table1.row2", "1.0.3_link": ["Communication.md#2.4.s5.table1.row2"]}, "A Person Object's \"mbox\" property is an Array of IRIs": {"name": "A Person Object's \"mbox\" property is an Array of IRIs", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row3.a", "1.0.2_ref": "7.6.table1.row3.a", "1.0.3_ref": "Communication 2.4.s5.table1.row3", "1.0.3_link": ["Communication.md#2.4.s5.table1.row3"]}, "A Person Object's \"mbox\" entries have the form \"mailto:emailaddress\"": {"name": "A Person Object's \"mbox\" entries have the form \"mailto:emailaddress\"", "1.0.2_ref_text": "Format, 7.6.table1.row3.a", "1.0.2_ref": "7.6.table1.row3.a", "1.0.3_ref": "Communication 2.4.s5.table1.row3", "1.0.3_link": ["Communication.md#2.4.s5.table1.row3"]}, "A Person Object's \"mbox_sha1sum\" property is an Array of Strings": {"name": "A Person Object's \"mbox_sha1sum\" property is an Array of Strings", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row4.a", "1.0.2_ref": "7.6.table1.row4.a", "1.0.3_ref": "Communication 2.4.s5.table1.row4", "1.0.3_link": ["Communication.md#2.4.s5.table1.row4"]}, "A Person Object's \"openid\" property is an Array of Strings": {"name": "A Person Object's \"openid\" property is an Array of Strings", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row5.a", "1.0.2_ref": "7.6.table1.row5.a", "1.0.3_ref": "Communication 2.4.s5.table1.row5", "1.0.3_link": ["Communication.md#2.4.s5.table1.row5"]}, "A Person Object's \"account\" property is an Array of Account Objects": {"name": "A Person Object's \"account\" property is an Array of Account Objects", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row6.a", "1.0.2_ref": "7.6.table1.row6.a", "1.0.3_ref": "Communication 2.4.s5.table1.row6", "1.0.3_link": ["Communication.md#2.4.s5.table1.row6"]}, "A Person Object uses an \"objectType\" property exactly one time": {"name": "A Person Object uses an \"objectType\" property exactly one time", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row1.c", "1.0.2_ref": "7.6.table1.row1.c", "1.0.3_ref": "Communication 2.4.s5.table1.row1", "1.0.3_link": ["Communication.md#2.4.s5.table1.row1"]}, "A Person Object uses a \"name\" property at most one time": {"name": "A Person Object uses a \"name\" property at most one time", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row2.c", "1.0.2_ref": "7.6.table1.row2.c", "1.0.3_ref": "Communication 2.4.s5.table1.row2", "1.0.3_link": ["Communication.md#2.4.s5.table1.row2"]}, "A Person Object uses a \"mbox\" property at most one time": {"name": "A Person Object uses a \"mbox\" property at most one time", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row3.c", "1.0.2_ref": "7.6.table1.row3.c", "1.0.3_ref": "Communication 2.4.s5.table1.row3", "1.0.3_link": ["Communication.md#2.4.s5.table1.row3"]}, "A Person Object uses a \"mbox_sha1sum\" property at most one time": {"name": "A Person Object uses a \"mbox_sha1sum\" property at most one time", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row4.c", "1.0.2_ref": "7.6.table1.row4.c", "1.0.3_ref": "Communication 2.4.s5.table1.row4", "1.0.3_link": ["Communication.md#2.4.s5.table1.row4"]}, "A Person Object uses an \"openid\" property at most one time": {"name": "A Person Object uses an \"openid\" property at most one time", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row5.c", "1.0.2_ref": "7.6.table1.row5.c", "1.0.3_ref": "Communication 2.4.s5.table1.row5", "1.0.3_link": ["Communication.md#2.4.s5.table1.row5"]}, "A Person Object uses an \"account\" property at most one time": {"name": "A Person Object uses an \"account\" property at most one time", "1.0.2_ref_text": "Multiplicity, 7.6.table1.row6.c", "1.0.2_ref": "7.6.table1.row6.c", "1.0.3_ref": "Communication 2.4.s5.table1.row6", "1.0.3_link": ["Communication.md#2.4.s5.table1.row6"]}, "An LRS populates the \"authority\" property if it is not provided in the Statement, based on header information with the Agent corresponding to the user (contained within the header)": {"name": "An LRS populates the \"authority\" property if it is not provided in the Statement, based on header information with the Agent corresponding to the user (contained within the header)", "1.0.2_ref_text": "Implicit, 4.1.9.b, 4.1.9.c", "1.0.2_ref": "Implicit, 4.1.9.b, 4.1.9.c", "1.0.3_ref": "Implicit, Data 2.4.9.s3.b4", "1.0.3_link": ["Data.md#2.4.9.s3.b4"]}, "should populate authority": {"name": "should populate authority"}, "A Voiding Statement cannot Target another Voiding Statement": {"name": "A Voiding Statement cannot Target another Voiding Statement", "1.0.2_ref_text": "4.3", "1.0.2_ref": "4.3", "1.0.3_ref": "Data 2.3.2.s2.b7", "1.0.3_link": ["Data.md#2.3.2.s2.b7"]}, "persist voided statement": {"name": "persist voided statement"}, "persist voiding statement": {"name": "persist voiding statement"}, "should fail when \"StatementRef\" points to a voiding statement": {"name": "should fail when \"StatementRef\" points to a voiding statement"}, "An LRS returns a ContextActivity in an array, even if only a single ContextActivity is returned": {"name": "An LRS returns a ContextActivity in an array, even if only a single ContextActivity is returned", "1.0.2_ref_text": "4.1.6.2.c, 4.1.6.2.d", "1.0.2_ref": "4.1.6.2.c, 4.1.6.2.d", "1.0.3_ref": "Data 2.4.6.2.s4.b3", "1.0.3_link": ["Data.md#2.4.6.2.s4.b3"]}, "post 200, get 200, statement has context which has contextActivities which has parent which is an array": {"name": "post 200, get 200, statement has context which has contextActivities which has parent which is an array"}, "post 200, get 200, statement has context which has contextActivities which has grouping which is an array": {"name": "post 200, get 200, statement has context which has contextActivities which has grouping which is an array"}, "post 200, get 200, statement has context which has contextActivities which has category which is an array": {"name": "post 200, get 200, statement has context which has contextActivities which has category which is an array"}, "post 200, get 200, statement has context which has contextActivities which has other which is an array": {"name": "post 200, get 200, statement has context which has contextActivities which has other which is an array"}, "post 200, get 200, statement has object which has context which has contextActivities which has parent which is an array": {"name": "post 200, get 200, statement has object which has context which has contextActivities which has parent which is an array"}, "post 200, get 200, statement has object which has context which has contextActivities which has grouping which is an array": {"name": "post 200, get 200, statement has object which has context which has contextActivities which has grouping which is an array"}, "post 200, get 200, statement has object which has context which has contextActivities which has category which is an array": {"name": "post 200, get 200, statement has object which has context which has contextActivities which has category which is an array"}, "post 200, get 200, statement has object which has context which has contextActivities which has other which is an array": {"name": "post 200, get 200, statement has object which has context which has contextActivities which has other which is an array"}, "An LRS rejects with error code 400 Bad Request, a Request which uses Attachments and does not have a \"Content-Type\" header with value \"application/json\" or \"multipart/mixed\"": {"name": "An LRS rejects with error code 400 Bad Request, a Request which uses Attachments and does not have a \"Content-Type\" header with value \"application/json\" or \"multipart/mixed\"", "1.0.2_ref_text": "Format, 4.1.11.a, 4.1.11.b", "1.0.2_ref": "4.1.11.a, 4.1.11.b", "1.0.3_ref": "Communication 1.5", "1.0.3_link": ["Communication.md#1.5"]}, "should succeed when attachment uses \"fileUrl\" and request content-type is \"application/json\"": {"name": "should succeed when attachment uses \"fileUrl\" and request content-type is \"application/json\""}, "should fail when attachment uses \"fileUrl\" and request content-type is \"multipart/form-data\"": {"name": "should fail when attachment uses \"fileUrl\" and request content-type is \"multipart/form-data\""}, "should succeed when attachment is raw data and request content-type is \"multipart/mixed\"": {"name": "should succeed when attachment is raw data and request content-type is \"multipart/mixed\""}, "should fail when attachment is raw data and request content-type is \"multipart/form-data\"": {"name": "should fail when attachment is raw data and request content-type is \"multipart/form-data\""}, "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content-Type\" header with value \"application/json\", and has a discrepancy in the number of Attachments vs. the number of fileURL members": {"name": "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content-Type\" header with value \"application/json\", and has a discrepancy in the number of Attachments vs. the number of fileURL members", "1.0.3_ref": "Data 2.4.11, Communication 1.5.1.s1.b2", "1.0.3_link": ["Data 2.4.11", "Communication 1.5.1.s1.b2"]}, "should fail when passing statement attachments and missing attachment\"s binary": {"name": "should fail when passing statement attachments and missing attachment\"s binary"}, "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and does not have a body header named \"Content-Type\" with value \"multipart/mixed\"": {"name": "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and does not have a body header named \"Content-Type\" with value \"multipart/mixed\"", "1.0.2_ref_text": "RFC 1341", "1.0.2_ref": "4.1.11.b, RFC 1341", "1.0.3_ref": "Data 2.4.11", "1.0.3_link": ["Data.md#2.4.11"]}, "should fail when attachment is raw data and first part content type is not \"application/json\"": {"name": "should fail when attachment is raw data and first part content type is not \"application/json\""}, "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and does not have a body header named \"boundary\"": {"name": "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and does not have a body header named \"boundary\"", "1.0.2_ref_text": "4.1.11.b, RFC 1341", "1.0.2_ref": "4.1.11.b, RFC 1341", "1.0.3_ref": "Data 2.4.11, Communication 1.5.2.s2.b2", "1.0.3_link": ["Data 2.4.11", "Communication 1.5.2.s2.b2"]}, "should fail if boundary not provided in body": {"name": "should fail if boundary not provided in body"}, "A Boundary is defined as the value of the body header named \"boundary\"": {"name": "A Boundary is defined as the value of the body header named \"boundary\"", "1.0.2_ref_text": "Definition, 4.1.11.b, RFC 1341", "1.0.2_ref": "4.1.11.b, RFC 1341", "1.0.3_ref": "Data 2.4.11, Communication 1.5.2.s2.b2", "1.0.3_link": ["Data 2.4.11", "Communication 1.5.2.s2.b2"]}, "should fail if boundary not provided in header": {"name": "should fail if boundary not provided in header"}, "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and does not have a Boundary before each \"Content-Type\" header": {"name": "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and does not have a Boundary before each \"Content-Type\" header", "1.0.2_ref_text": "4.1.11.b, RFC 1341", "1.0.2_ref": "4.1.11.b, RFC 1341", "1.0.3_ref": "Data 2.4.11, Communication 1.5.2.s2.b2", "1.0.3_link": ["Data 2.4.11", "Communication 1.5.2.s2.b2"]}, "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and does not the first document part with a \"Content-Type\" header with a value of \"application/json\"": {"name": "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and does not the first document part with a \"Content-Type\" header with a value of \"application/json\"", "1.0.2_ref_text": "RFC 1341, 4.1.11.b.a", "1.0.2_ref": "RFC 1341, 4.1.11.b.a", "1.0.3_ref": "Data 2.4.11, Communication 1.5.2.s2.b1", "1.0.3_link": ["Data 2.4.11", "Communication 1.5.2.s2.b1"]}, "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and does not have all of the Statements in the first document part": {"name": "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and does not have all of the Statements in the first document part", "1.0.2_ref_text": "RFC 1341, 4.1.11.b.a", "1.0.2_ref": "RFC 1341, 4.1.11.b.a", "1.0.3_ref": "Data 2.4.11, Communication 1.5.2.s2.b1", "1.0.3_link": ["Data 2.4.11", "Communication 1.5.2.s2.b1"]}, "should fail when statements separated into multiple parts": {"name": "should fail when statements separated into multiple parts"}, "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and for any part except the first does not have a Header named \"X-Experience-API-Hash\" with a value of one of those found in a \"sha2\" property of a Statement in the first part of this document": {"name": "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and for any part except the first does not have a Header named \"X-Experience-API-Hash\" with a value of one of those found in a \"sha2\" property of a Statement in the first part of this document", "1.0.2_ref_text": "4.1.11.b.c, 4.1.11.b.d", "1.0.2_ref": "4.1.11.b.c, 4.1.11.b.d", "1.0.3_ref": "Data 2.4.11, Communication 1.5.2.s2.b2.b3, Communication 1.5.2.s1.b4", "1.0.3_link": ["Data 2.4.11", "Communication 1.5.2.s2.b2.b3", "Communication 1.5.2.s1.b4"]}, "should fail when attachments missing header \"X-Experience-API-Hash\"": {"name": "should fail when attachments missing header \"X-Experience-API-Hash\""}, "should fail when attachments header \"X-Experience-API-Hash\" does not match \"sha2\"": {"name": "should fail when attachments header \"X-Experience-API-Hash\" does not match \"sha2\""}, "An LRS rejects with error code 400 Bad Request, a Request which does not use a \"X-Experience-API-Version\" header name to any Resource except the About Resource": {"name": "An LRS rejects with error code 400 Bad Request, a Request which does not use a \"X-Experience-API-Version\" header name to any Resource except the About Resource", "1.0.2_ref_text": "Format, 6.2.a, 6.2.f, 7.7.f", "1.0.2_ref": "6.2.a, 6.2.f, 7.7.f", "1.0.3_ref": ["Communication 3.3.s4.b1", "Communication 3.3.s3.b7", "Communication 2.8.s5.b4"], "1.0.3_link": ["Communication.md#3.3.s4.b1", "Communication.md#3.3.s3.b7", "Communication.md#2.8.s5.b4"]}, "should pass when GET without header \"X-Experience-API-Version\"": {"name": "should pass when GET without header \"X-Experience-API-Version\""}, "should fail when statement GET without header \"X-Experience-API-Version\"": {"name": "should fail when statement GET without header \"X-Experience-API-Version\""}, "should fail when statement POST without header \"X-Experience-API-Version\"": {"name": "should fail when statement POST without header \"X-Experience-API-Version\""}, "should fail when statement PUT without header \"X-Experience-API-Version\"": {"name": "should fail when statement PUT without header \"X-Experience-API-Version\""}, "An LRS MUST set the X-Experience-API-Version header to the latest patch version": {"name": "An LRS MUST set the X-Experience-API-Version header to the latest patch version", "1.0.2_ref_text": "4.1.10.b", "1.0.2_ref": "4.1.10.b", "1.0.3_ref": "Communication 3.3.s3.b2", "1.0.3_link": ["Communication.md#3.3.s3.b2"]}, "An LRS sends a header response with \"X-Experience-API-Version\" as the name and \"1.0.3\" as the value": {"name": "An LRS sends a header response with \"X-Experience-API-Version\" as the name and \"1.0.3\" as the value", "1.0.3_ref": "Communication 3.3.s3.b1, Communication 3.3.s3.b2", "1.0.3_link": ["Communication.md#3.3.s3.b1", "Communication.md#3.3.s3.b2"]}, "should respond with header \"version\" set to \"1.0.2\"": {"name": "should respond with header \"version\" set to \"1.0.2\""}, "An LRS will not modify Statements based on a \"version\" before \"1.0.1\"": {"name": "An LRS will not modify Statements based on a \"version\" before \"1.0.1\"", "1.0.2_ref_text": "6.2.l", "1.0.2_ref": "6.2.l", "1.0.3_ref": "Communication 3.3.s3.b4", "1.0.3_link": ["Communication.md#3.3.s3.b4"]}, "should not convert newer version format to prior version format": {"name": "should not convert newer version format to prior version format"}, "An LRS rejects with error code 400 Bad Request any request to an Resource which uses a parameter with differing case": {"name": "An LRS rejects with error code 400 Bad Request any request to an Resource which uses a parameter with differing case", "1.0.2_ref_text": "7.0.b", "1.0.2_ref": "7.0.b", "1.0.3_ref": "Communication 3.2.s3.b8", "1.0.3_link": ["Communication.md#3.2.s3.b8"]}, "should fail on PUT statement when not using \"statementId\"": {"name": "should fail on PUT statement when not using \"statementId\""}, "should fail on GET statement when not using \"statementId\"": {"name": "should fail on GET statement when not using \"statementId\""}, "should fail on GET statement when not using \"voidedStatementId\"": {"name": "should fail on GET statement when not using \"voidedStatementId\""}, "should fail on GET statement when not using \"agent\"": {"name": "should fail on GET statement when not using \"agent\""}, "should fail on GET statement when not using \"verb\"": {"name": "should fail on GET statement when not using \"verb\""}, "should fail on GET statement when not using \"activity\"": {"name": "should fail on GET statement when not using \"activity\""}, "should fail on GET statement when not using \"registration\"": {"name": "should fail on GET statement when not using \"registration\""}, "should fail on GET statement when not using \"related_activities\"": {"name": "should fail on GET statement when not using \"related_activities\""}, "should fail on GET statement when not using \"related_agents\"": {"name": "should fail on GET statement when not using \"related_agents\""}, "should fail on GET statement when not using \"since\"": {"name": "should fail on GET statement when not using \"since\""}, "should fail on GET statement when not using \"until\"": {"name": "should fail on GET statement when not using \"until\""}, "should fail on GET statement when not using \"limit\"": {"name": "should fail on GET statement when not using \"limit\""}, "should fail on GET statement when not using \"format\"": {"name": "should fail on GET statement when not using \"format\""}, "should fail on GET statement when not using \"attachments\"": {"name": "should fail on GET statement when not using \"attachments\""}, "should fail on GET statement when not using \"ascending\"": {"name": "should fail on GET statement when not using \"ascending\""}, "An LRS does not process any batch of Statements in which one or more Statements is rejected and if necessary, restores the LRS to the state in which it was before the batch began processing": {"name": "An LRS does not process any batch of Statements in which one or more Statements is rejected and if necessary, restores the LRS to the state in which it was before the batch began processing", "1.0.2_ref_text": "7.0.c, **Implicit**", "1.0.2_ref": "7.0.c, **Implicit**", "1.0.3_ref": "Communication 3.2.s3.b9", "1.0.3_link": ["Communication.md#3.2.s3.b9"]}, "should not persist any statements on a single failure": {"name": "should not persist any statements on a single failure"}, "An LRS has a Statement Resource with endpoint \"base IRI\"+\"/statements\"": {"name": "An LRS has a Statement Resource with endpoint \"base IRI\"+\"/statements\"", "1.0.2_ref_text": "7.2", "1.0.2_ref": "7.2", "1.0.3_ref": "Communication 2.0", "1.0.3_link": ["Communication.md#2.0"]}, "should allow \"/statements\" POST": {"name": "should allow \"/statements\" POST"}, "should allow \"/statements\" PUT": {"name": "should allow \"/statements\" PUT"}, "should allow \"/statements\" GET": {"name": "should allow \"/statements\" GET"}, "An LRS's Statement Resource accepts PUT requests": {"name": "An LRS's Statement Resource accepts PUT requests", "1.0.2_ref_text": "7.2.1", "1.0.2_ref": "7.2.1", "1.0.3_ref": "Communication 2.1.1.s1", "1.0.3_link": ["Communication.md#2.1.1.s1"]}, "should persist statement using \"PUT\"": {"name": "should persist statement using \"PUT\""}, "An LRS's Statement Resource accepts PUT requests only if it contains a \"statementId\" parameter": {"name": "An LRS's Statement Resource accepts PUT requests only if it contains a \"statementId\" parameter", "1.0.2_ref_text": "Multiplicity, 7.2.1.table1.a", "1.0.2_ref": "7.2.1.table1.a", "1.0.3_ref": "Communication 2.1.1.s1.table1.row1", "1.0.3_link": ["Communication.md#2.1.1.s1.table1.row1"]}, "should persist statement using \"statementId\" parameter": {"name": "should persist statement using \"statementId\" parameter"}, "should fail without using \"statementId\" parameter": {"name": "should fail without using \"statementId\" parameter"}, "An LRS's Statement Resource accepts PUT requests only if the \"statementId\" parameter is a String": {"name": "An LRS's Statement Resource accepts PUT requests only if the \"statementId\" parameter is a String", "1.0.2_ref_text": "Type, 7.2.1.table1.b", "1.0.2_ref": "7.2.1.table1.a", "1.0.3_ref": "Communication 2.1.1.s1.table1.row1", "1.0.3_link": ["Communication.md#2.1.1.s1.table1.row1"]}, "should fail statement using \"statementId\" parameter as boolean": {"name": "should fail statement using \"statementId\" parameter as boolean"}, "should fail statement using \"statementId\" parameter as object": {"name": "should fail statement using \"statementId\" parameter as object"}, "An LRS cannot modify a Statement, state, or Object in the event it receives a Statement with statementID equal to a Statement in the LRS already.": {"name": "An LRS cannot modify a Statement, state, or Object in the event it receives a Statement with statementID equal to a Statement in the LRS already.", "1.0.2_ref_text": "7.2.1.a, 7.2.2.b", "1.0.2_ref": "7.2.1.a, 7.2.2.b", "1.0.3_ref": "Communication 2.1.1.s2.b2", "1.0.3_link": ["Communication.md#2.1.1.s2.b2"]}, "should not update statement with matching \"statementId\" on POST": {"name": "should not update statement with matching \"statementId\" on POST"}, "should not update statement with matching \"statementId\" on PUT": {"name": "should not update statement with matching \"statementId\" on PUT"}, "An LRS's Statement Resource upon processing a successful PUT request returns code 204 No Content": {"name": "An LRS's Statement Resource upon processing a successful PUT request returns code 204 No Content", "1.0.2_ref_text": "7.2.1", "1.0.2_ref": "7.2.1", "1.0.3_ref": "Communication 2.1.1.s1", "1.0.3_link": ["Communication.md#2.1.1.s1"]}, "should persist statement and return status 204": {"name": "should persist statement and return status 204"}, "An LRS's Statement Resource rejects with error code 409 Conflict any Statement with the \"statementID\" parameter equal to a Statement in the LRS already **Implicit**": {"name": "An LRS's Statement Resource rejects with error code 409 Conflict any Statement with the \"statementID\" parameter equal to a Statement in the LRS already **Implicit**", "1.0.2_ref_text": "7.2.1.b, 7.2.2.b", "1.0.2_ref": "7.2.1.b, 7.2.2.b", "1.0.3_ref": "Communication 2.1.1.s2.b2", "1.0.3_link": ["Communication.md#2.1.1.s2.b2"]}, "should return 409 or 204 when statement ID already exists on POST": {"name": "should return 409 or 204 when statement ID already exists on POST"}, "should return 409 or 204 when statement ID already exists on PUT": {"name": "should return 409 or 204 when statement ID already exists on PUT"}, "An LRS's Statement Resource accepts POST requests": {"name": "An LRS's Statement Resource accepts POST requests", "1.0.2_ref_text": "7.2.2", "1.0.2_ref": "7.2.2", "1.0.3_ref": "Communication 2.1.2.s1", "1.0.3_link": ["Communication.md#2.1.2.s1"]}, "should persist statement using \"POST\"": {"name": "should persist statement using \"POST\""}, "The LRS will NOT reject a GET request which returns an empty \"statements\" property": {"name": "The LRS will NOT reject a GET request which returns an empty \"statements\" property", "1.0.2_ref_text": "**Implicit**, 4.2.table1.row1.b", "1.0.2_ref": "**Implicit**, 4.2.table1.row1.b", "1.0.3_ref": "Implicit, Communication 2.1.3.s2.b4", "1.0.3_link": ["Communication.md#2.1.3.s2.b4"]}, "should return empty array list": {"name": "should return empty array list"}, "An LRS's Statement Resource upon processing a successful POST request returns code 200 OK and all Statement UUIDs within the POST **Implicit**": {"name": "An LRS's Statement Resource upon processing a successful POST request returns code 200 OK and all Statement UUIDs within the POST **Implicit**", "1.0.2_ref_text": "7.2.2", "1.0.2_ref": "7.2.2", "1.0.3_ref": "Communication 2.1.2.s1", "1.0.3_link": ["Communication.md#2.1.2.s1"]}, "should persist statement using \"POST\" and return array of IDs": {"name": "should persist statement using \"POST\" and return array of IDs"}, "A \"more\" property is an IRL": {"name": "A \"more\" property is an IRL", "1.0.2_ref_text": "Format, 4.2.table1.row2.a", "1.0.2_ref": "4.2.table1.row2.a", "1.0.3_ref": "Data 2.5.s2.table1.row2", "1.0.3_link": ["Data.md#2.5.s2.table1.row2"]}, "should return \"more\" property as an IRL": {"name": "should return \"more\" property as an IRL"}, "The \"more\" property is an empty string if the entire results of the original GET request have been returned": {"name": "The \"more\" property is an empty string if the entire results of the original GET request have been returned", "1.0.2_ref_text": "4.2.table1.row2.b", "1.0.2_ref": "4.2.table1.row2.b", "1.0.3_ref": "Data 2.5.s2.table1.row2", "1.0.3_link": ["Data.md#2.5.s2.table1.row2"]}, "should return empty \"more\" property when all statements returned": {"name": "should return empty \"more\" property when all statements returned"}, "If not empty, the \"more\" property's IRL refers to a specific container object corresponding to the next page of results from the orignal GET request": {"name": "If not empty, the \"more\" property's IRL refers to a specific container object corresponding to the next page of results from the orignal GET request", "1.0.2_ref_text": "4.2.table1.row1.b", "1.0.2_ref": "4.2.table1.row1.b", "1.0.3_ref": "Data 2.5.s2.table1.row2", "1.0.3_link": ["Data.md#2.5.s2.table1.row2"]}, "should return \"more\" which refers to next page of results": {"name": "should return \"more\" which refers to next page of results"}, "A \"more\" property's referenced container object follows the same rules as the original GET request, originating with a single \"statements\" property and a single \"more\" property": {"name": "A \"more\" property's referenced container object follows the same rules as the original GET request, originating with a single \"statements\" property and a single \"more\" property", "1.0.3_ref": "Data.md#2.5.s2.table1.row2", "1.0.3_link": ["Data.md#2.5.s2.table1.row2"]}, "A Voided Statement is defined as a Statement that is not a Voiding Statement and is the Target of a Voiding Statement within the LRS": {"name": "A Voided Statement is defined as a Statement that is not a Voiding Statement and is the Target of a Voiding Statement within the LRS", "1.0.2_ref_text": "4.2.c", "1.0.2_ref": "4.2.c", "1.0.3_ref": "Data 2.3.2.s2.b3", "1.0.3_link": ["Data.md#2.3.2.s2.b3"]}, "should return a voided statement when using GET \"voidedStatementId\"": {"name": "should return a voided statement when using GET \"voidedStatementId\""}, "An LRS's Statement Resource, upon processing a successful GET request, can only return a Voided Statement if that Statement is specified in the voidedStatementId parameter of that request": {"name": "An LRS's Statement Resource, upon processing a successful GET request, can only return a Voided Statement if that Statement is specified in the voidedStatementId parameter of that request", "1.0.2_ref_text": "7.2.4.a", "1.0.2_ref": "7.2.4.a", "1.0.3_ref": "Communication 2.1.4.s1.b1", "1.0.3_link": ["Communication.md#2.1.4.s1.b1"]}, "should not return a voided statement if using GET \"statementId\"": {"name": "should not return a voided statement if using GET \"statementId\""}, "LRS's Statement Resource accepts GET requests": {"name": "LRS's Statement Resource accepts GET requests", "1.0.2_ref_text": "7.2.3", "1.0.2_ref": "7.2.3", "1.0.3_ref": "Communication 2.1.3.s1", "1.0.3_link": ["Communication.md#2.1.3.s1"]}, "should return using GET": {"name": "should return using GET"}, "An LRS's Statement Resource can process a GET request with \"statementId\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"statementId\" as a parameter", "1.0.2_ref_text": "7.2.3", "1.0.2_ref": "7.2.3", "1.0.3_ref": "Communication 2.1.3.s1.table1.row1", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row1"]}, "should process using GET with \"statementId\"": {"name": "should process using GET with \"statementId\""}, "An LRS's Statement Resource can process a GET request with \"voidedStatementId\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"voidedStatementId\" as a parameter", "1.0.2_ref_text": "7.2.3", "1.0.2_ref": "7.2.3", "1.0.3_ref": "Communication 2.1.3.s1.table1.row2", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row2"]}, "should process using GET with \"voidedStatementId\"": {"name": "should process using GET with \"voidedStatementId\""}, "An LRS's Statement Resource rejects with error code 400 a GET request with both \"statementId\" and anything other than \"attachments\" or \"format\" as parameters": {"name": "An LRS's Statement Resource rejects with error code 400 a GET request with both \"statementId\" and anything other than \"attachments\" or \"format\" as parameters", "1.0.2_ref_text": "7.2.3.a, 7.2.3.b", "1.0.2_ref": "7.2.3.a, 7.2.3.b", "1.0.3_ref": "Communication 2.1.3.s2.b2", "1.0.3_link": ["Communication.md#2.1.3.s2.b2"]}, "persist statement": {"name": "persist statement"}, "should fail when using \"statementId\" with \"agent\"": {"name": "should fail when using \"statementId\" with \"agent\""}, "should fail when using \"statementId\" with \"verb\"": {"name": "should fail when using \"statementId\" with \"verb\""}, "should fail when using \"statementId\" with \"activity\"": {"name": "should fail when using \"statementId\" with \"activity\""}, "should fail when using \"statementId\" with \"registration\"": {"name": "should fail when using \"statementId\" with \"registration\""}, "should fail when using \"statementId\" with \"related_activities\"": {"name": "should fail when using \"statementId\" with \"related_activities\""}, "should fail when using \"statementId\" with \"related_agents\"": {"name": "should fail when using \"statementId\" with \"related_agents\""}, "should fail when using \"statementId\" with \"since\"": {"name": "should fail when using \"statementId\" with \"since\""}, "should fail when using \"statementId\" with \"until\"": {"name": "should fail when using \"statementId\" with \"until\""}, "should fail when using \"statementId\" with \"limit\"": {"name": "should fail when using \"statementId\" with \"limit\""}, "should fail when using \"statementId\" with \"ascending\"": {"name": "should fail when using \"statementId\" with \"ascending\""}, "should pass when using \"statementId\" with \"format\"": {"name": "should pass when using \"statementId\" with \"format\""}, "should pass when using \"statementId\" with \"attachments\"": {"name": "should pass when using \"statementId\" with \"attachments\""}, "An LRS's Statement Resource can process a GET request with \"agent\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"agent\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row3", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row3"]}, "should process using GET with \"agent\"": {"name": "should process using GET with \"agent\""}, "An LRS's Statement Resource can process a GET request with \"verb\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"verb\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row4", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row4"]}, "should process using GET with \"verb\"": {"name": "should process using GET with \"verb\""}, "An LRS's Statement Resource can process a GET request with \"activity\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"activity\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row5", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row5"]}, "should process using GET with \"activity\"": {"name": "should process using GET with \"activity\""}, "An LRS's Statement Resource can process a GET request with \"registration\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"registration\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row6", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row6"]}, "should process using GET with \"registration\"": {"name": "should process using GET with \"registration\""}, "An LRS's Statement Resource can process a GET request with \"related_activities\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"related_activities\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row7", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row7"]}, "should process using GET with \"related_activities\"": {"name": "should process using GET with \"related_activities\""}, "An LRS's Statement Resource can process a GET request with \"related_agents\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"related_agents\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row8", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row8"]}, "should process using GET with \"related_agents\"": {"name": "should process using GET with \"related_agents\""}, "An LRS's Statement Resource can process a GET request with \"since\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"since\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row9", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row9"]}, "should process using GET with \"since\"": {"name": "should process using GET with \"since\""}, "An LRS's Statement Resource can process a GET request with \"until\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"until\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row10", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row10"]}, "should process using GET with \"until\"": {"name": "should process using GET with \"until\""}, "An LRS's Statement Resource can process a GET request with \"limit\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"limit\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row11", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row11"]}, "should process using GET with \"limit\"": {"name": "should process using GET with \"limit\""}, "An LRS's Statement Resource can process a GET request with \"format\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"format\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row12", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row12"]}, "should process using GET with \"format\"": {"name": "should process using GET with \"format\""}, "An LRS's Statement Resource can process a GET request with \"attachments\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"attachments\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row13", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row13"]}, "should process using GET with \"attachments\"": {"name": "should process using GET with \"attachments\""}, "An LRS's Statement Resource can process a GET request with \"ascending\" as a parameter": {"name": "An LRS's Statement Resource can process a GET request with \"ascending\" as a parameter  **Implicit**", "1.0.2_ref": "Implicit", "1.0.3_ref": "Communication 2.1.3.s1.table1.row14", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row14"]}, "should process using GET with \"ascending\"": {"name": "should process using GET with \"ascending\""}, "An LRS's Statement Resource rejects with error code 400 a GET request with both \"voidedStatementId\" and anything other than \"attachments\" or \"format\" as parameters": {"name": "An LRS's Statement Resource rejects with error code 400 a GET request with both \"voidedStatementId\" and anything other than \"attachments\" or \"format\" as parameters", "1.0.2_ref_text": "7.2.3.a, 7.2.3.b", "1.0.2_ref": "7.2.3.a, 7.2.3.b", "1.0.3_ref": "Communication 2.1.3.s2.b2", "1.0.3_link": ["Communication.md#2.1.3.s2.b2"]}, "should fail when using \"voidedStatementId\" with \"agent\"": {"name": "should fail when using \"voidedStatementId\" with \"agent\""}, "should fail when using \"voidedStatementId\" with \"verb\"": {"name": "should fail when using \"voidedStatementId\" with \"verb\""}, "should fail when using \"voidedStatementId\" with \"activity\"": {"name": "should fail when using \"voidedStatementId\" with \"activity\""}, "should fail when using \"voidedStatementId\" with \"registration\"": {"name": "should fail when using \"voidedStatementId\" with \"registration\""}, "should fail when using \"voidedStatementId\" with \"related_activities\"": {"name": "should fail when using \"voidedStatementId\" with \"related_activities\""}, "should fail when using \"voidedStatementId\" with \"related_agents\"": {"name": "should fail when using \"voidedStatementId\" with \"related_agents\""}, "should fail when using \"voidedStatementId\" with \"since\"": {"name": "should fail when using \"voidedStatementId\" with \"since\""}, "should fail when using \"voidedStatementId\" with \"until\"": {"name": "should fail when using \"voidedStatementId\" with \"until\""}, "should fail when using \"voidedStatementId\" with \"limit\"": {"name": "should fail when using \"voidedStatementId\" with \"limit\""}, "should fail when using \"voidedStatementId\" with \"ascending\"": {"name": "should fail when using \"voidedStatementId\" with \"ascending\""}, "should pass when using \"voidedStatementId\" with \"format\"": {"name": "should pass when using \"voidedStatementId\" with \"format\""}, "should pass when using \"voidedStatementId\" with \"attachments\"": {"name": "should pass when using \"voidedStatementId\" with \"attachments\""}, "An LRS's Statement Resource upon processing a successful GET request with a \"statementId\" parameter, returns code 200 OK and a single Statement with the corresponding \"id\".": {"name": "An LRS's Statement Resource upon processing a successful GET request with a \"statementId\" parameter, returns code 200 OK and a single Statement with the corresponding \"id\".", "1.0.2_ref_text": "7.2.3", "1.0.2_ref": "7.2.3", "1.0.3_ref": "Communication 2.1.3.s1", "1.0.3_link": ["Communication.md#2.1.3.s1"]}, "should retrieve statement using \"statementId\"": {"name": "should retrieve statement using \"statementId\""}, "An LRS's Statement Resource upon processing a successful GET request with a \"voidedStatementId\" parameter, returns code 200 OK and a single Statement with the corresponding \"id\".": {"name": "An LRS's Statement Resource upon processing a successful GET request with a \"voidedStatementId\" parameter, returns code 200 OK and a single Statement with the corresponding \"id\".", "1.0.2_ref_text": "7.2.3", "1.0.2_ref": "7.2.3", "1.0.3_ref": "Communication 2.1.3.s1", "1.0.3_link": ["Communication.md#2.1.3.s1"]}, "An LRS's Statement Resource upon processing a successful GET request with neither a \"statementId\" nor a \"voidedStatementId\" parameter, returns code 200 OK and a StatementResult Object.": {"name": "An LRS's Statement Resource upon processing a successful GET request with neither a \"statementId\" nor a \"voidedStatementId\" parameter, returns code 200 OK and a StatementResult Object.", "1.0.2_ref_text": "7.2.3", "1.0.2_ref": "7.2.3", "1.0.3_ref": "Communication 2.1.3.s1", "1.0.3_link": ["Communication.md#2.1.3.s1"]}, "persist substatement": {"name": "persist substatement"}, "should return StatementResult using GET without \"statementId\" or \"voidedStatementId\"": {"name": "should return StatementResult using GET without \"statementId\" or \"voidedStatementId\""}, "should return StatementResult using GET with \"agent\"": {"name": "should return StatementResult using GET with \"agent\""}, "should return StatementResult using GET with \"verb\"": {"name": "should return StatementResult using GET with \"verb\""}, "should return StatementResult using GET with \"activity\"": {"name": "should return StatementResult using GET with \"activity\""}, "should return StatementResult using GET with \"registration\"": {"name": "should return StatementResult using GET with \"registration\""}, "should return StatementResult using GET with \"related_activities\"": {"name": "should return StatementResult using GET with \"related_activities\""}, "should return StatementResult using GET with \"related_agents\"": {"name": "should return StatementResult using GET with \"related_agents\""}, "should return StatementResult using GET with \"since\"": {"name": "should return StatementResult using GET with \"since\""}, "should return StatementResult using GET with \"until\"": {"name": "should return StatementResult using GET with \"until\""}, "should return StatementResult using GET with \"limit\"": {"name": "should return StatementResult using GET with \"limit\""}, "should return StatementResult using GET with \"ascending\"": {"name": "should return StatementResult using GET with \"ascending\""}, "should return StatementResult using GET with \"format\"": {"name": "should return StatementResult using GET with \"format\""}, "should return multipart response format StatementResult using GET with \"attachments\" parameter as true": {"name": "should return multipart response format StatementResult using GET with \"attachments\" parameter as true"}, "should not return multipart response format using GET with \"attachments\" parameter as false": {"name": "should not return multipart response format using GET with \"attachments\" parameter as false"}, "An LRS's \"X-Experience-API-Consistent-Through\" header's value is not before (temporal) any of the \"stored\" values of any of the returned Statements": {"name": "An LRS's \"X-Experience-API-Consistent-Through\" header's value is not before (temporal) any of the \"stored\" values of any of the returned Statements", "1.0.2_ref_text": "7.2.3.c", "1.0.2_ref": "7.2.3.c", "1.0.3_ref": "Communication 2.1.3.s2.b5", "1.0.3_link": ["Communication.md#2.1.3.s2.b5"]}, "should return \"X-Experience-API-Consistent-Through\" when using GET for statements": {"name": "should return \"X-Experience-API-Consistent-Through\" when using GET for statements"}, "An LRS's Statement Resource upon processing a GET request, returns a header with name \"X-Experience-API-Consistent-Through\" regardless of the code returned.": {"name": "An LRS's Statement Resource upon processing a GET request, returns a header with name \"X-Experience-API-Consistent-Through\" regardless of the code returned.", "1.0.2_ref_text": "7.2.3.c", "1.0.2_ref": "7.2.3.c", "1.0.3_ref": "Communication 2.1.3.s2.b5, XAPI-00153", "1.0.3_link": ["Communication.md#2.1.3.s2.b5"]}, "should return \"X-Experience-API-Consistent-Through\" using GET": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET"}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"agent\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"agent\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"verb\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"verb\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"activity\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"activity\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"registration\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"registration\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"related_activities\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"related_activities\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"related_agents\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"related_agents\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"since\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"since\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"until\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"until\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"limit\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"limit\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"ascending\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"ascending\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"format\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"format\""}, "should return \"X-Experience-API-Consistent-Through\" using GET with \"attachments\"": {"name": "should return \"X-Experience-API-Consistent-Through\" using GET with \"attachments\""}, "An LRS's \"X-Experience-API-Consistent-Through\" header is an ISO 8601 combined date and time": {"name": "An LRS's \"X-Experience-API-Consistent-Through\" header is an ISO 8601 combined date and time", "1.0.2_ref_text": "Type, 7.2.3.c", "1.0.2_ref": "7.2.3.c", "1.0.3_ref": "Communication 2.1.3.s2.b5", "1.0.3_link": ["Communication.md#2.1.3.s2.b5"]}, "should return valid \"X-Experience-API-Consistent-Through\" using GET": {"name": "should return valid \"X-Experience-API-Consistent-Through\" using GET"}, "The Statements within the \"statements\" property will correspond to the filtering criterion sent in with the GET request": {"name": "The Statements within the \"statements\" property will correspond to the filtering criterion sent in with the GET request", "1.0.3_ref": "Communication 2.1.3.s1, XAPI-00164", "1.0.3_link": ["Communication.md#2.1.3.s1"]}, "A \"statements\" property which is too large for a single page will create a container for each additional page": {"name": "A \"statements\" property which is too large for a single page will create a container for each additional page", "1.0.3_ref": "Data 2.5.s2.table1.row1", "1.0.3_link": ["Data.md#2.5.s2.table1.row1"]}, "A \"statements\" property is an Array of Statements": {"name": "A \"statements\" property is an Array of Statements", "1.0.2_ref_text": "Type, 4.2.table1.row1.a", "1.0.2_ref": "4.2.table1.row1.a", "1.0.3_ref": "Data 2.5.s2.table1.row1", "1.0.3_link": ["Data.md#2.5.s2.table1.row1"]}, "should return StatementResult with statements as array using GET without \"statementId\" or \"voidedStatementId\"": {"name": "should return StatementResult with statements as array using GET without \"statementId\" or \"voidedStatementId\""}, "should return StatementResult with statements as array using GET with \"agent\"": {"name": "should return StatementResult with statements as array using GET with \"agent\""}, "should return StatementResult with statements as array using GET with \"verb\"": {"name": "should return StatementResult with statements as array using GET with \"verb\""}, "should return StatementResult with statements as array using GET with \"activity\"": {"name": "should return StatementResult with statements as array using GET with \"activity\""}, "should return StatementResult with statements as array using GET with \"registration\"": {"name": "should return StatementResult with statements as array using GET with \"registration\""}, "should return StatementResult with statements as array using GET with \"related_activities\"": {"name": "should return StatementResult with statements as array using GET with \"related_activities\""}, "should return StatementResult with statements as array using GET with \"related_agents\"": {"name": "should return StatementResult with statements as array using GET with \"related_agents\""}, "should return StatementResult with statements as array using GET with \"since\"": {"name": "should return StatementResult with statements as array using GET with \"since\""}, "should return StatementResult with statements as array using GET with \"until\"": {"name": "should return StatementResult with statements as array using GET with \"until\""}, "should return StatementResult with statements as array using GET with \"limit\"": {"name": "should return StatementResult with statements as array using GET with \"limit\""}, "should return StatementResult with statements as array using GET with \"ascending\"": {"name": "should return StatementResult with statements as array using GET with \"ascending\""}, "should return StatementResult with statements as array using GET with \"format\"": {"name": "should return StatementResult with statements as array using GET with \"format\""}, "should return StatementResult with statements as array using GET with \"attachments\"": {"name": "should return StatementResult with statements as array using GET with \"attachments\""}, "An LRS's Statement Resource, upon processing a successful GET request wishing to return a Voided Statement still returns Statements which target it": {"name": "An LRS's Statement Resource, upon processing a successful GET request wishing to return a Voided Statement still returns Statements which target it", "1.0.2_ref_text": "7.2.4.b", "1.0.2_ref": "7.2.4.b", "1.0.3_ref": "Communication 2.1.4.s1.b2", "1.0.3_link": ["Communication.md#2.1.4.s1.b2"]}, "persist object with statement references": {"name": "persist object with statement references"}, "should only return Object StatementRef when using \"since\"": {"name": "should only return Object StatementRef when using \"since\""}, "should only return voiding statement when using \"until\"": {"name": "should only return voiding statement when using \"until\""}, "should only return Object StatementRef when using \"limit\"": {"name": "should only return Object StatementRef when using \"limit\""}, "should return StatementRef and voiding statement when not using \"since\", \"until\", \"limit\"": {"name": "should return StatementRef and voiding statement when not using \"since\", \"until\", \"limit\""}, "Miscellaneous Requirements": {"name": "Miscellaneous Requirements"}, "JSON parser validates this": {"name": "JSON parser validates this", "1.0.2_ref": "4.2.table1.row1.b", "1.0.3_ref": "part2.2.5.table1.row1.b"}, "Handled internally by LRS": {"name": "Handled internally by LRS", "1.0.2_ref": "6.1.a", "1.0.3_ref": "part3.1.4.a"}, "Handled by templating": {"name": "Handled by templating", "1.0.2_ref": "4.3.b", "1.0.3_ref": "part2.2.3.2.b"}, "Using requirement: An LRS rejects with error code 405 Method Not Allowed to any request to an Resource which uses a method not in this specification **Implicit ONLY in that HTML normally does this behavior**": {"name": "Using requirement: An LRS rejects with error code 405 Method Not Allowed to any request to an Resource which uses a method not in this specification **Implicit ONLY in that HTML normally does this behavior**", "1.0.2_ref": "7.2", "1.0.3_ref": "Communication 2.1", "1.0.3_link": ["Communication.md#2.1"]}, "All of these \"defined\" aren't really tests, rather ways to disambiguate future tests.": {"name": "All of these \"defined\" aren't really tests, rather ways to disambiguate future tests.", "1.0.2_ref": "7.2.2.e", "1.0.3_ref": "Communication 2.1.2.e", "1.0.3_link": ["Communication.md#2.1.2.s2.b3"]}, "Not concerned with \"Content-Type\" when use a GET request": {"name": "Not concerned with \"Content-Type\" when use a GET request", "1.0.2_ref": "7.2.3.d, 7.2.3.e", "1.0.3_ref": "Communication 2.1.3.s2.b7", "1.0.3_link": ["Communication.md#2.1.3.s2.b7"]}, "RFC 1341: MIME-Version header field is required at the top level of a message. It is not required for each body part of a multipart entity": {"name": "RFC 1341: MIME-Version header field is required at the top level of a message. It is not required for each body part of a multipart entity", "1.0.2_ref": "4.1.11.b, RFC 1341", "1.0.3_ref": "part3.1.5.2.b, RFC2046"}, "An LRS doesn't make any adjustments to incoming Statements that are not specifically mentioned in this section": {"name": "An LRS doesn't make any adjustments to incoming Statements that are not specifically mentioned in this section", "1.0.2_ref_text": "4.1.12.d, Varies", "1.0.2_ref": "4.1.12.d, Varies", "1.0.3_ref": "Data 2.2.s4.b8", "1.0.3_link": ["Data.md#2.2.s4.b8"]}, "An LRS rejects with error code 400 Bad Request, a Request whose \"authority\" is a Group and consists of non-O-Auth Agents": {"name": "An LRS rejects with error code 400 Bad Request, a Request whose \"authority\" is a Group and consists of non-O-Auth Agents", "1.0.2_ref_text": "4.1.9.a", "1.0.2_ref": "4.1.9.a", "1.0.3_ref": "Data 2.4.9.s3.b1", "1.0.3_link": ["Data.md#2.4.9.s3.b1"]}, "An LRS rejects a Statement of bad authorization, either authentication needed or failed credentials, with error code 401 Unauthorized": {"name": "An LRS rejects a Statement of bad authorization (either authentication needed or failed credentials) with error code 401 Unauthorized", "1.0.2_ref_text": "7.1", "1.0.2_ref": "7.1", "1.0.3_ref": "Communication 4.0.s2.b2, XAPI-00334", "1.0.3_link": ["Communication.md#4.0.s2.b2"]}, "An LRS rejects a Statement of insufficient permissions (credentials are valid, but not adequate) with error code 403 Forbidden": {"name": "An LRS rejects a Statement of insufficient permissions (credentials are valid, but not adequate) with error code 403 Forbidden", "1.0.2_ref_text": "7.1", "1.0.2_ref": "7.1", "1.0.3_ref": "Communication 3.2.s3.b11", "1.0.3_link": ["Communication.md#3.2.s3.b11"]}, "An LRS rejects with error code 403 Forbidden a Request whose \"authority\" is a Agent or Group that is not authorized": {"name": "An LRS rejects with error code 403 Forbidden a Request whose \"authority\" is a Agent or Group that is not authorized", "1.0.2_ref_text": "4.1.9.b, 6.4.2", "1.0.2_ref": "4.1.9.b, 6.4.2", "1.0.3_ref": "Communication 3.2.s3.b11", "1.0.3_link": ["Communication.md#3.2.s3.b11"]}, "An LRS rejects with error code 400 Bad Request any request to an Resource which uses a parameter not recognized by the LRS": {"name": "An LRS rejects with error code 400 Bad Request any request to an Resource which uses a parameter not recognized by the LRS", "1.0.2_ref_text": "7.0.a", "1.0.2_ref": "7.0.a", "1.0.3_ref": "Communication 3.2.s2.b1", "1.0.3_link": ["Communication.md#3.2.s2.b1"]}, "An LRS accepts a valid POST request containing a GET request returning 200 OK and the StatementResult Object.": {"name": "An LRS accepts a valid POST request containing a GET request returning 200 OK and the StatementResult Object.", "1.0.2_ref_text": "7.2.3, 7.2.2.e", "1.0.2_ref": "7.2.3, 7.2.2.e", "1.0.3_ref": "Communication 1.3, Communication 2.1.2.s2.b3, XAPI-00148", "1.0.3_link": ["Communication.md#1.3", "Communication.md#2.1.2.s2.b3"]}, "An LRS makes no modifications to stored data for any rejected request": {"name": "An LRS makes no modifications to stored data for any rejected request", "1.0.2_ref_text": "7.2.3, 7.2.2.e", "1.0.2_ref": "7.2.3, 7.2.2.e", "1.0.3_ref": "Communication 2.1.2.s2.b4", "1.0.3_link": ["Communication.md#2.1.2.s2.b4"]}, "An LRS's Statement Resource, upon processing a successful GET request, will return a single \"statements\" property": {"name": "An LRS's Statement Resource, upon processing a successful GET request, will return a single \"statements\" property", "1.0.2_ref_text": "7.2.3, 7.2.2.e", "1.0.2_ref": "7.2.3, 7.2.2.e", "1.0.3_ref": "Communication 2.1.3.s1", "1.0.3_link": ["Communication.md#2.1.3.s1"]}, "An LRS's Statement Resource, upon processing a successful GET request, will return a single \"more\" property": {"name": "An LRS's Statement Resource, upon processing a successful GET request, will return a single \"more\" property", "1.0.2_ref_text": "7.2.3, 7.2.2.e", "1.0.2_ref": "7.2.3, 7.2.2.e", "1.0.3_ref": "Communication 2.1.3.s1", "1.0.3_link": ["Communication.md#2.1.3.s1"]}, "LRS must validate and store statement signatures if they are provided": {"name": "LRS must validate and store statement signatures if they are provided", "1.0.3_ref": ["Data 2.6"], "1.0.3_link": ["Data.md#2.6"]}, "LRS must reject with 400 a signed statement with malformed signature - bad contentType": {"name": "LRS must reject with 400 a signed statement with malformed signature - bad contentType", "1.0.3_ref": ["Data 2.6.s4.b1"], "1.0.3_link": ["Data.md#2.6.s4.b1"]}, "LRS must reject with 400 a signed statement with malformed signature - bad algorithm": {"name": "LRS must reject with 400 a signed statement with malformed signature - bad algorithm", "1.0.3_ref": ["Data 2.6.s4.b4"], "1.0.3_link": ["Data.md#2.6.s4.b4"]}, "A Signed Statement MUST include a JSON web signature, JWS": {"name": "A Signed Statement MUST include a JSON web signature, JWS", "1.0.3_ref": ["Data 2.6.s4.b1, XAPI-00115"], "1.0.3_link": ["Data.md#2.6.s4.b1"]}, "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and for any part except the first does not have a Header named \"Content-Transfer-Encoding\" with a value of \"binary\"": {"name": "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which uses Attachments, has a \"Content Type\" header with value \"multipart/mixed\", and for any part except the first does not have a Header named \"Content-Transfer-Encoding\" with a value of \"binary\"", "1.0.3_ref": "Data 2.4.11", "1.0.3_link": ["Data.md#2.4.11"]}, "An LRS rejects with error code 400 Bad Request, a GET Request which uses Attachments, has a \"Content-Type\" header with value \"application/json\", and has the \"attachments\" filter attribute set to \"true\"": {"name": "LRS must reject with 400 a signed statement with malformed signature - bad JSON serialization of statement", "1.0.3_ref": ["Data 3.2"], "1.0.3_link": ["Data.md#3.2"]}, "An LRS's Statement Resource will reject a GET request having the \"attachment\" parameter set to \"false\" and the Content-Type field in the header set to anything but \"application/json\"": {"name": "An LRS's Statement Resource will reject a GET request having the \"attachment\" parameter set to \"false\" and the Content-Type field in the header set to anything but \"application/json\"", "1.0.3_ref": "Data 2.4.11", "1.0.3_link": ["Data.md#2.4.11"]}, "An LRS's Statement Resource will reject a GET request having the \"attachment\" parameter set to \"true\" if it does not follow the rest of the attachment rules": {"name": "An LRS's Statement Resource will reject a GET request having the \"attachment\" parameter set to \"true\" if it does not follow the rest of the attachment rules", "1.0.3_ref": ["Data 2.4.11"], "1.0.3_link": ["Data.md#2.4.11"]}, "An LRS's Statement Resource will reject a GET request having the \"attachment\" parameter set to \"false\" if it includes attachment raw data": {"name": "An LRS's Statement Resource will reject a GET request having the \"attachment\" parameter set to \"false\" if it includes attachment raw data", "1.0.3_ref": ["Data 2.4.11"], "1.0.3_link": ["Data.md#2.4.11"]}, "Formatting Requirements": {"name": "Formatting Requirements", "1.0.3_ref": ["Data 2.2"], "1.0.3_link": ["Data.md#2.2"]}, "Statement Lifecycle Requirements": {"name": "Statement Lifecycle Requirements", "1.0.3_ref": ["Data 2.3"], "1.0.3_link": ["Data.md#2.3"]}, "Voiding Requirements": {"name": "Voiding Requirements", "1.0.3_ref": ["Data 2.3.2"], "1.0.3_link": ["Data.md#2.3.2"]}, "Id Property Requirements": {"name": "Id Property Requirements", "1.0.3_ref": ["Data 2.4.1"], "1.0.3_link": ["Data.md#2.4.1"]}, "Actor Property Requirements": {"name": "Actor Property Requirements", "1.0.3_ref": ["Data 2.4.2"], "1.0.3_link": ["Data.md#2.4.2"]}, "Verb Property Requirements": {"name": "Verb Property Requirements", "1.0.3_ref": ["Data 2.4.3"], "1.0.3_link": ["Data.md#2.4.3"]}, "Object Property Requirements": {"name": "Object Property Requirements", "1.0.3_ref": ["Data 2.4.4"], "1.0.3_link": ["Data.md#2.4.4"]}, "Result Property Requirements": {"name": "Result Property Requirements", "1.0.3_ref": ["Data 2.4.5"], "1.0.3_link": ["Data.md#2.4.5"]}, "Context Property Requirements": {"name": "Context Property Requirements", "1.0.3_ref": ["Data 2.4.6"], "1.0.3_link": ["Data.md#2.4.6"]}, "Timestamp Property Requirements": {"name": "Timestamp Property Requirements", "1.0.3_ref": ["Data 2.4.7"], "1.0.3_link": ["Data.md#2.4.7"]}, "Stored Property Requirements": {"name": "Stored Property Requirements", "1.0.3_ref": ["Data 2.4.8"], "1.0.3_link": ["Data.md#2.4.8"]}, "Authority Property Requirements": {"name": "Authority Property Requirements", "1.0.3_ref": ["Data 2.4.9"], "1.0.3_link": ["Data.md#2.4.9"]}, "Version Property Requirements": {"name": "Version Property Requirements", "1.0.3_ref": ["Data 2.4.10"], "1.0.3_link": ["Data.md#2.4.10"]}, "Attachments Property Requirements": {"name": "Attachments Property Requirements", "1.0.3_ref": ["Data 2.4.11"], "1.0.3_link": ["Data.md#2.4.11"]}, "Retrieval of Statements": {"name": "Retrieval of Statements", "1.0.3_ref": ["Data 2.5"], "1.0.3_link": ["Data.md#2.5"]}, "Signed Statements": {"name": "Signed Statements", "1.0.3_ref": ["Data 2.6"], "1.0.3_link": ["Data.md#2.6"]}, "Metadata Requirements": {"name": "Metadata Requirements", "1.0.3_ref": ["Data 3.0"], "1.0.3_link": ["Data.md#3.0"]}, "Special Data Types and Rules": {"name": "Special Data Types and Rules", "1.0.3_ref": ["Data 4.0"], "1.0.3_link": ["Data.md#4.0"]}, "HEAD Request Implementation Requirements": {"name": "HEAD Request Implementation Requirements", "1.0.3_ref": ["Communication 1.1"], "1.0.3_link": ["Communication.md#1.1"]}, "Headers Requirements": {"name": "Headers Requirements", "1.0.3_ref": ["Communication 1.2"], "1.0.3_link": ["Communication.md#1.2"]}, "Alternate Request Syntax Requirements": {"name": "Alternate Request Syntax Requirements", "1.0.3_ref": ["Communication 1.3"], "1.0.3_link": ["Communication.md#1.3"]}, "Encoding Requirements": {"name": "Encoding Requirements", "1.0.3_ref": ["Communication 1.4"], "1.0.3_link": ["Communication.md#1.4"]}, "Content Type Requirements": {"name": "Content Type Requirements", "1.0.3_ref": ["Communication 1.5"], "1.0.3_link": ["Communication.md#1.5"]}, "Statement Resource Requirements": {"name": "Statement Resource Requirements", "1.0.3_ref": ["Communication 2.1"], "1.0.3_link": ["Communication.md#2.1"]}, "State Resource Requirements": {"name": "State Resource Requirements", "1.0.3_ref": ["Communication 2.3"], "1.0.3_link": ["Communication.md#2.3"]}, "Agents Resource Requirements": {"name": "Agents Resource Requirements", "1.0.3_ref": ["Communication 2.4"], "1.0.3_link": ["Communication.md#2.4"]}, "Activities Resource Requirements": {"name": "Activities Resource Requirements", "1.0.3_ref": ["Communication 2.5"], "1.0.3_link": ["Communication.md#2.5"]}, "Agent Profile Resource Requirements": {"name": "Agent Profile Resource Requirements", "1.0.3_ref": ["Communication 2.6"], "1.0.3_link": ["Communication.md#2.6"]}, "Activity Profile Resource Requirements": {"name": "Activity Profile Resource Requirements", "1.0.3_ref": ["Communication 2.7"], "1.0.3_link": ["Communication.md#2.7"]}, "About Resource Requirements": {"name": "About Resource Requirements", "1.0.3_ref": ["Communication 2.8"], "1.0.3_link": ["Communication.md#2.8"]}, "Concurrency Requirements": {"name": "Concurrency Requirements", "1.0.3_ref": ["Communication 3.1"], "1.0.3_link": ["Communication.md#3.1"]}, "Error Codes Requirements": {"name": "Error Codes Requirements", "1.0.3_ref": ["Communication 3.2"], "1.0.3_link": ["Communication.md#3.2"]}, "Versioning Requirements": {"name": "Versioning Requirements", "1.0.3_ref": ["Communication 3.3"], "1.0.3_link": ["Communication.md#3.3"]}, "Authentication Requirements": {"name": "Authentication Requirements", "1.0.3_ref": ["Communication 4.0"], "1.0.3_link": ["Communication.md#4.0"]}, "All Strings are encoded and interpreted as UTF-8": {"name": "All Strings are encoded and interpreted as UTF-8", "1.0.3_ref": "Communication 1.4.s1.b1", "1.0.3_link": ["Communication.md#1.4.s1.b1"]}, "An LRS must support HTTP/1.1 entity tags (ETags) to implement optimistic concurrency control when handling Resources where PUT may overwrite existing data": {"name": "An LRS must support HTTP/1.1 entity tags (ETags) to implement optimistic concurrency control when handling Resources where PUT may overwrite existing data", "1.0.3_ref": "Communication 3.1", "1.0.3_link": ["Communication.md#3.1"]}, "An LRS's Activity Profile Resource rejects a DELETE request with \"profileId\" as a parameter if it is not type \"String\" with error code 400 Bad Request": {"name": "An LRS's Activity Profile Resource rejects a DELETE request with \"profileId\" as a parameter if it is not type \"String\" with error code 400 Bad Request", "1.0.3_ref": "Communication 2.7", "1.0.3_link": ["Communication.md#2.7"]}, "An LRS rejects with error code 400 Bad Request, a Request which does not use a \"X-Experience-API-Version\" header name to any API except the About API": {"name": "An LRS rejects with error code 400 Bad Request, a Request which does not use a \"X-Experience-API-Version\" header name to any API except the About API", "1.0.3_ref": "Communication 2.8", "1.0.3_link": ["Communication.md#2.8"]}, "Statements returned by an LRS MUST retain the header version they are accepted with": {"name": "Statements returned by an LRS MUST retain the header version they are accepted with", "1.0.3_ref": "Communication 3.3", "1.0.3_link": ["Communication.md#3.3"]}, "All Objects are well-created JSON Objects": {"name": "All Objects are well-created JSON Objects", "1.0.3_ref": "Data 2.1", "1.0.3_link": ["Data.md#2.1"]}, "An LRS rejects with error code 400 Bad Request a Statement containing IRL or IRI values without a scheme.": {"name": "An LRS rejects with error code 400 Bad Request a Statement containing IRL or IRI values without a scheme.", "1.0.3_ref": "Data 2.2.s4.b1.b8", "1.0.3_link": ["Data.md#2.2.s4.b1.b8"]}, "An LRS rejects with error code 400 Bad Request a Statement which uses any non-format-following key or value, including the empty string, where a string with a particular format, such as mailto IRI, UUID, or IRI, is required.": {"name": "An LRS rejects with error code 400 Bad Request a Statement which uses any non-format-following key or value, including the empty string, where a string with a particular format, such as mailto IRI, UUID, or IRI, is required.", "1.0.3_ref": "Data 2.2.s4.b4, XAPI-00007", "1.0.3_link": ["Data.md#2.2.s4.b4"]}, "An LRS rejects with error code 400 Bad Request a Statement where the case of a key does not match the case specified in this specification.": {"name": "An LRS rejects with error code 400 Bad Request a Statement where the case of a key does not match the case specified in this specification. ", "1.0.3_ref": "Data 2.2.s4.b1.b5, XAPI-00010", "1.0.3_link": ["Data.md#2.2.s4.b1.b5"]}, "An LRS rejects with error code 400 Bad Request a Statement where the case of a value restricted to enumerated values does not match an enumerated value given in this specification exactly.": {"name": "An LRS rejects with error code 400 Bad Request a Statement where the case of a value restricted to enumerated values does not match an enumerated value given in this specification exactly.", "1.0.3_ref": "Data 2.2.s4.b1.b6, XAPI-00009", "1.0.3_link": ["Data.md#2.2.s4.b1.b6"]}, "The LRS rejects with error code 400 Bad Request a token with does not validate as matching the RFC 5646 standard in the sequence of token lengths for language map keys.": {"name": "The LRS rejects with error code 400 Bad Request a token with does not validate as matching the RFC 5646 standard in the sequence of token lengths for language map keys.", "1.0.3_ref": "Data 2.2.s4.b2, XAPI-00013", "1.0.3_link": ["Data.md#2.2.s4.b2"]}, "An \"mbox\" property has the form \"mailto:email address\" and is an IRI": {"name": "An \"mbox\" property has the form \"mailto:email address\" and is an IRI", "1.0.3_ref": "Data *******.s3.table1.row1, XAPI-00038", "1.0.3_link": ["Data.md#*******.s3.table1.row1"]}, "An Interaction Component's \"description\" property is a Language Map.": {"name": "An Interaction Component's \"description\" property is a Language Map.", "1.0.3_ref": "Data *******.s15.table1.row2, XAPI-00062", "1.0.3_link": ["Data.md#*******.s15.table1.row2"]}, "Statements that use an Agent or Group as an Object MUST specify an \"objectType\" property.": {"name": "Statements that use an Agent or Group as an Object MUST specify an \"objectType\" property.", "1.0.3_ref": "Data *******.s1.b1, XAPI-00065", "1.0.3_link": ["Data.md#*******.s1.b1"]}, "The LRS rejects with error code 400 Bad Request parameter values which do not validate to the same standards required for values of the same types in Statements": {"name": "The LRS rejects with error code 400 Bad Request parameter values which do not validate to the same standards required for values of the same types in Statements", "1.0.3_ref": "Data 2.2.s4.b4, XAPI-00012", "1.0.3_link": ["Data.md#2.2.s4.b4"]}, "A \"score\" Object's \"scaled\" property is a decimal number between -1 and 1, inclusive.": {"name": "A \"score\" Object's \"scaled\" property is a decimal number between -1 and 1, inclusive.", "1.0.3_ref": "Data 2.4.5.1.s2.table1.row1, XAPI-00083", "1.0.3_link": ["Data.md#2.4.5.1.s2.table1.row1"]}, "A \"score\" Object's \"raw\" property is a decimal number between min and max, if present and otherwise unrestricted, inclusive": {"name": "A \"score\" Object's \"raw\" property is a decimal number between min and max (if present, otherwise unrestricted), inclusive", "1.0.3_ref": "Data 2.4.5.1.s2.table1.row2, XAPI-00082", "1.0.3_link": ["Data.md#2.4.5.1.s2.table1.row2"]}, "A \"score\" Object's \"min\" property is a decimal number less than the \"max\" property, if it is present.": {"name": "A \"score\" Object's \"min\" property is a decimal number less than the \"max\" property, if it is present.", "1.0.3_ref": "Data 2.4.5.1.s2.table1.row3, XAPI-00081", "1.0.3_link": ["Data.md#2.4.5.1.s2.table1.row3"]}, "Statements returned by an LRS MUST retain the version property they are accepted with": {"name": "Statements returned by an LRS MUST retain the version property they are accepted with", "1.0.3_ref": "Data 2.4.10, XAPI-00332", "1.0.3_link": ["Data.md#2.4.10"]}, "Signed statement with \"RS384\"": {"name": "Signed statement with \"RS384\"", "1.0.3_ref": "Data 2.6.s4.b1", "1.0.3_link": ["Data.md#2.6.s4.b1"]}, "Signed statement with \"RS512\"": {"name": "Signed statement with \"RS512\"", "1.0.3_ref": "Data 2.6.s4.b1", "1.0.3_link": ["Data.md#2.6.s4.b1"]}, "The JWS signature MUST use an algorithm of \"RS256\", \"RS384\", or \"RS512\".": {"name": "The JWS signature MUST use an algorithm of \"RS256\", \"RS384\", or \"RS512\".", "1.0.3_ref": "Data 2.6.s4.b4, XAPI-00117", "1.0.3_link": ["Data.md#2.6.s4.b4"]}, "The JWS signature MUST have a payload of a valid JSON serialization of the complete Statement before the signature was added.": {"name": "The JWS signature MUST have a payload of a valid JSON serialization of the complete Statement before the signature was added.", "1.0.3_ref": "Data 2.6.s4.b3, XAPI-00116", "1.0.3_link": ["Data.md#2.6.s4.b3"]}, "A Timestamp MUST preserve precision to at least milliseconds, 3 decimal points beyond seconds.": {"name": " A Timestamp MUST preserve precision to at least milliseconds, 3 decimal points beyond seconds.", "1.0.3_ref": "Data 4.5.s1.b3, XAPI-00122", "1.0.3_link": ["Data.md#4.5.s1.b3"]}, "A Duration MUST be expressed using the format for Duration in ISO 8601:2004(E) section 4.4.3.2.": {"name": "A Duration MUST be expressed using the format for Duration in ISO 8601:2004(E) section 4.4.3.2.", "1.0.3_ref": "Data 4.6.s1.b1, XAPI-00124", "1.0.3_link": ["Data.md#4.6.s1.b1"]}, "If the \"Accept-Language\" header is present as part of the GET request to the Statement API and the \"format\" parameter is set to \"canonical\", the LRS MUST apply this data to choose the matching language in the response.": {"name": "If the \"Accept-Language\" header is present as part of the GET request to the Statement API and the \"format\" parameter is set to \"canonical\", the LRS MUST apply this data to choose the matching language in the response.", "1.0.3_ref": "Communication 2.1.3.s1.table1.row11, XAPI-00172", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row11"]}, "An LRSs Statement API, upon receiving a GET request, MUST have a \"Content-Type\" header": {"name": "An LRSs Statement API, upon receiving a GET request, MUST have a \"Content-Type\" header", "1.0.3_ref": "Communication 2.1.3.s1.table1.row14, XAPI-00165", "1.0.3_link": ["Communication.md#2.1.3.s1.table1.row14"]}, "An LRSs Statement Resource not return attachment data and only return application/json if the \"attachment\" parameter set to \"false\"": {"name": "An LRSs Statement Resource not return attachment data and only return application/json if the \"attachment\" parameter set to \"false\"", "1.0.3_ref": "Communication 2.1.3.s1.b1, XAPI-00161", "1.0.3_link": ["Communication.md#2.1.3.s1.b1"]}, "An LRSs Agents Resource rejects a GET request with \"agent\" as a parameter if it is not a valid, in structure, Agent with error code 400 Bad Request": {"name": "An LRSs Agents Resource rejects a GET request with \"agent\" as a parameter if it is not a valid, in structure, Agent with error code 400 Bad Request", "1.0.3_ref": "Communication 2.4, XAPI-00249", "1.0.3_link": ["Communication.md#2.4"]}, "An LRS's Activity Profile Resource, rejects a POST request if the document is found and either doucment is not a valid JSON Object": {"name": "An LRS's Activity Profile Resource, rejects a POST request if the document is found and either doucment is not a valid JSON Object", "1.0.3_ref": "Communication 2.7.s4.table1.row2, XAPI-00313", "1.0.3_link": ["Communication.md#2.7.s4.table1.row2"]}, "An LRS's must reject, with 400 Bad Request, a POST request to the Acitvity Profile Resource which contains name/value pairs with invalid JSON and the Content-Type header is \"application/json\"": {"name": "An LRS's must reject, with 400 Bad Request, a POST request to the Acitvity Profile Resource which contains name/value pairs with invalid JSON and the Content-Type header is \"application/json\"", "1.0.3_ref": "Communication 2.7.s4.table1.row2, XAPI-00314", "1.0.3_link": ["Communication.md#2.7.s4.table1.row2"]}, "An LRS must support HTTP Basic Authentication": {"name": "An LRS must support HTTP Basic Authentication", "1.0.3_ref": "Communication 4.0, XAPI-00335", "1.0.3_link": ["Communication.md#4.0"]}, "An LRSs State Resource, rejects a POST request if the document is found and either document is not a valid JSON Object": {"name": "An LRSs State Resource, rejects a POST request if the document is found and either document is not a valid JSON Object", "1.0.3_ref": "Communication 2.3.s3.table1.row3, XAPI-00229", "1.0.3_link": ["Communication.md#2.3.s3.table1.row3"]}, "The Activity Object must contain all available information about an activity from any statements who target the same \"activityId\". For example, LRS accepts two statements each with a different language description of an activity using the exact same \"activityId\". The LRS must return both language descriptions when a GET request is made to the Activities endpoint for that \"activityId\"": {"name": "The Activity Object must contain all available information about an activity from any statements who target the same \"activityId\". For example, LRS accepts two statements each with a different language description of an activity using the exact same \"activityId\". The LRS must return both language descriptions when a GET request is made to the Activities endpoint for that \"activityId\"", "1.0.3_ref": "Communication 2.5.s1.table1.row1, XAPI-00254", "1.0.3_link": ["Communication.md#2.5.s1.table1.row1"]}, "An LRSs Agent Profile Resource, rejects a POST request if the document is found and either documents type is not \"application/json\" with error code 400 Bad Request": {"name": "An LRSs Agent Profile Resource, rejects a POST request if the document is found and either documents type is not \"application/json\" with error code 400 Bad Request", "1.0.3_ref": "Communication 2.6.s3.table1.row3, XAPI-00278", "1.0.3_link": ["Communication.md#2.6.s3.table1.row3"]}, "The \"more\" property is absent or an empty string (no whitespace) if the entire results of the original GET request have been returned.": {"name": "The \"more\" property is absent or an empty string (no whitespace) if the entire results of the original GET request have been returned.", "1.0.3_ref": "Data 2.5.s2.table1.row2, XAPI-00109", "1.0.3_link": ["Data.md#2.5.s2.table1.row2"]}, "An LRS's Agent Profile Resource, rejects a POST request if the document is found and either document is not a valid JSON Object": {"name": "An LRS's Agent Profile Resource, rejects a POST request if the document is found and either document is not a valid JSON Object", "1.0.3_ref": "Communication 2.6, XAPI-00281", "1.0.3_link": ["Communication.md#2.6, XAPI-00281"]}, "An LRS must reject with 400 Bad Request a POST request to the Activitiy Profile Resource which contains name/value pairs with invalid JSON and the Content-Type header is 'application/json'": {"name": "An LRS must reject with 400 Bad Request a POST request to the Activitiy Profile Resource which contains name/value pairs with invalid JSON and the Content-Type header is 'application/json'", "1.0.3_ref": "Communication 2.6, XAPI-00284", "1.0.3_link": ["Communication.md#2.6"]}, "An LRS must reject with 400 Bad Request a POST request to the State Resource which contains name/value pairs with invalid JSON and the Content-Type header is 'application/json'": {"name": "An LRS must reject with 400 Bad Request a POST request to the State Resource which contains name/value pairs with invalid JSON and the Content-Type header is 'application/json'", "1.0.3_ref": "Communication 2.3, XAPI-00235", "1.0.3_link": ["Communication.md#2.3"]}, "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which has excess multi-part sections that are not attachments.": {"name": "An LRS rejects with error code 400 Bad Request, a PUT or POST Request which has excess multi-part sections that are not attachments.", "1.0.3_ref": "Communication 1.5.1.s1.b2, Data 2.4.11, XAPI-00128", "1.0.3_link": ["Communication.md#1.5.1.s1.b2", "Data.md#2.4.11"]}, "An LRS's Statement API, upon processing a successful GET request, will return a single \"statements\" property and a single \"more\" property.": {"name": "An LRS's Statement API, upon processing a successful GET request, will return a single \"statements\" property and a single \"more\" property.", "1.0.3_ref": "Data 2.5.s2.table1, XAPI-00113", "1.0.3_link": ["Data.md#2.5.s2.table1"]}, "An LRS will reject an alternate request syntax which contains any extra information with error code 400 Bad Request": {"name": "An LRS will reject an alternate request syntax which contains any extra information with error code 400 Bad Request", "1.0.3_ref": "Communication 1.3.s3.b4", "1.0.3_link": ["Communication.md#1.3.s3.b4"]}, "The LRS MUST support the Alternate Request Syntax": {"name": "The LRS MUST support the Alternate Request Syntax", "1.0.3_ref": "Communication 1.3.s3.b15, XAPI-00336", "1.0.3_link": ["Communication.md#1.3.s3.b15"]}, "During an alternate request syntax the LRS treats the listed form parameters, 'Authorization', 'X-Experience-API-Version', 'Content-Type', 'Content-Length', 'If-Match' and 'If-None-Match', as header parameters": {"name": "During an alternate request syntax the LRS treats the listed form parameters, 'Authorization', 'X-Experience-API-Version', 'Content-Type', 'Content-Length', 'If-Match' and 'If-None-Match', as header parameters", "1.0.3_ref": "Communication 1.3.s3.b7", "1.0.3_link": ["Communication.md#1.3.s3.b7"]}, "An LRS will reject an alternate request syntax sending content which does not have a form parameter with the name of \"content\"": {"name": "An LRS will reject an alternate request syntax sending content which does not have a form parameter with the name of \"content\"", "1.0.3_ref": "Communication 1.3.s3.b4", "1.0.3_link": ["Communication.md#1.3.s3.b4"]}}